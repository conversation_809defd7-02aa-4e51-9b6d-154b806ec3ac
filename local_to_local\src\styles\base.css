*,
*::before,
*::after {
    box-sizing: border-box;
    margin: 0;
}

::-webkit-scrollbar {
    width: 8px;
    height: 8px;
}
::-webkit-scrollbar-thumb {
    background: rgba(100, 127, 255, 0.3);
    border-radius: 6px;
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
}
::-webkit-scrollbar-track {
    background: rgba(245, 248, 252, 0.8);
    border-radius: 6px;
}

html,
body {
    width: 100%;
    height: 100%;
    font-family:
        'SF Pro Display',
        'Segoe UI',
        'Roboto',
        -webkit-system-font,
        system-ui,
        sans-serif !important;
    /* 浅色科技风渐变背景 */
    background: linear-gradient(135deg, 
        #f8fafc 0%, 
        #e2e8f0 25%, 
        #cbd5e1 50%, 
        #94a3b8 75%, 
        #64748b 100%);
    /* 添加动态背景效果 */
    background-size: 400% 400%;
    animation: lightTechGradient 20s ease infinite;
    transition:
        color 0.5s,
        background-color 0.5s;
    line-height: 1.5;
    font-size: 14px;
    font-weight: 400;
    color: #1e293b;
    text-rendering: optimizeLegibility;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    margin: 0;
    padding: 0;
    overflow: hidden;
    position: relative;
}

/* 浅色动态渐变动画 */
@keyframes lightTechGradient {
    0% {
        background-position: 0% 50%;
    }
    50% {
        background-position: 100% 50%;
    }
    100% {
        background-position: 0% 50%;
    }
}

/* 机械科技感装饰背景 */
body::before {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        /* 网格线条 */
        linear-gradient(rgba(100, 127, 255, 0.1) 1px, transparent 1px),
        linear-gradient(90deg, rgba(100, 127, 255, 0.1) 1px, transparent 1px),
        /* 科技点阵 */
        radial-gradient(circle at 25% 25%, rgba(100, 127, 255, 0.15) 2px, transparent 2px),
        radial-gradient(circle at 75% 75%, rgba(120, 158, 254, 0.1) 1px, transparent 1px);
    background-size: 50px 50px, 50px 50px, 100px 100px, 80px 80px;
    animation: mechanicalMove 30s linear infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes mechanicalMove {
    0% {
        transform: translate(0, 0);
    }
    100% {
        transform: translate(-50px, -50px);
    }
}

/* 浮动的科技元素 */
body::after {
    content: '';
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: 
        radial-gradient(circle at 20% 30%, rgba(100, 127, 255, 0.08) 0%, transparent 50%),
        radial-gradient(circle at 80% 70%, rgba(120, 158, 254, 0.06) 0%, transparent 50%),
        radial-gradient(circle at 40% 80%, rgba(147, 197, 253, 0.05) 0%, transparent 50%);
    animation: floatingElements 25s ease-in-out infinite;
    pointer-events: none;
    z-index: 1;
}

@keyframes floatingElements {
    0%, 100% {
        opacity: 0.6;
        transform: scale(1);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
}

#app {
    width: 100%;
    height: 100%;
    padding: 20px;
    position: relative;
    z-index: 2;
    /* 磨砂玻璃效果容器 */
    backdrop-filter: blur(25px);
    border-radius: 24px;
    background: rgba(255, 255, 255, 0.85);
    border: 2px solid rgba(255, 255, 255, 0.3);
    box-shadow: 
        0 12px 40px rgba(0, 0, 0, 0.1),
        inset 0 1px 0 rgba(255, 255, 255, 0.8),
        0 0 0 1px rgba(100, 127, 255, 0.1);
}

/* 全局磨砂玻璃效果样式类 */
.glass-effect {
    background: rgba(255, 255, 255, 0.7);
    backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.4);
    border-radius: 16px;
    box-shadow: 
        0 8px 32px rgba(0, 0, 0, 0.08),
        inset 0 1px 0 rgba(255, 255, 255, 0.6),
        0 0 0 1px rgba(100, 127, 255, 0.08);
}

.glass-button {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(15px);
    border: 1px solid rgba(100, 127, 255, 0.2);
    color: #334155;
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    overflow: hidden;
}

.glass-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(100, 127, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.glass-button:hover {
    background: rgba(255, 255, 255, 0.9);
    border-color: rgba(100, 127, 255, 0.4);
    transform: translateY(-1px);
    box-shadow: 
        0 8px 25px rgba(100, 127, 255, 0.15),
        inset 0 1px 0 rgba(255, 255, 255, 0.8);
    color: #1e293b;
}

.glass-button:hover::before {
    left: 100%;
}

.glass-button.active {
    background: linear-gradient(135deg, 
        rgba(100, 127, 255, 0.9) 0%, 
        rgba(120, 158, 254, 0.8) 100%);
    border-color: rgba(100, 127, 255, 0.6);
    color: #ffffff;
    box-shadow: 
        0 0 25px rgba(100, 127, 255, 0.4),
        inset 0 1px 0 rgba(255, 255, 255, 0.3),
        0 4px 15px rgba(100, 127, 255, 0.2);
}

/* 机械科技感发光效果 */
.tech-glow {
    position: relative;
}

.tech-glow::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, 
        transparent, 
        rgba(100, 127, 255, 0.3), 
        rgba(120, 158, 254, 0.2), 
        transparent);
    border-radius: inherit;
    z-index: -1;
    animation: mechanicalGlow 4s ease-in-out infinite;
}

@keyframes mechanicalGlow {
    0%, 100% {
        opacity: 0.4;
        transform: scale(1);
    }
    50% {
        opacity: 0.8;
        transform: scale(1.01);
    }
}

/* 机械分割线效果 */
.mechanical-divider {
    height: 2px;
    background: linear-gradient(90deg, 
        transparent, 
        rgba(100, 127, 255, 0.4), 
        rgba(120, 158, 254, 0.6), 
        rgba(100, 127, 255, 0.4), 
        transparent);
    position: relative;
    margin: 16px 0;
}

.mechanical-divider::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 50%;
    transform: translate(-50%, -50%);
    width: 8px;
    height: 8px;
    background: #647fff;
    border-radius: 50%;
    box-shadow: 
        0 0 10px rgba(100, 127, 255, 0.6),
        0 0 20px rgba(100, 127, 255, 0.3);
}

/* 科技感文本样式 */
.tech-text {
    background: linear-gradient(135deg, #334155, #475569, #64748b);
    background-clip: text;
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    font-weight: 600;
    text-shadow: none;
}

/* 机械按钮样式 */
.mechanical-button {
    position: relative;
    background: linear-gradient(145deg, #f1f5f9, #e2e8f0);
    border: 2px solid rgba(100, 127, 255, 0.2);
    color: #334155;
    transition: all 0.3s ease;
    overflow: hidden;
}

.mechanical-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(145deg, 
        rgba(100, 127, 255, 0.1), 
        rgba(120, 158, 254, 0.05));
    opacity: 0;
    transition: opacity 0.3s ease;
}

.mechanical-button:hover::before {
    opacity: 1;
}

.mechanical-button:hover {
    transform: translateY(-2px);
    border-color: rgba(100, 127, 255, 0.4);
    box-shadow: 
        0 10px 25px rgba(100, 127, 255, 0.15),
        0 0 0 1px rgba(100, 127, 255, 0.1);
}
