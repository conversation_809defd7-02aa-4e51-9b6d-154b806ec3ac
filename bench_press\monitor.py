import requests
import json
import time
import uuid
import hashlib
import datetime
import concurrent.futures
from datetime import datetime
import statistics
from pathlib import Path
import aiohttp
import asyncio
import random
#from mysql_util import connect_PBDB,update_data,insert_data_and_get_id,execute_query,insert_data,insert_data_id_not_exists

TEST_BASE_URL = "106.63.5.45:19099"
BASE_URL = "203.176.93.140:19099"
IMAGE_URL = f"http://{BASE_URL}/lm/text2image"
SCENE_URL = f"http://{BASE_URL}/lm/text2scene"
appKey = 'bgju38949K5J6e54NSgh831'
appSecret = 'aT9034u5jFd8u3k5djk4tPg89ojhNu8wos'

def sendWxMsg(txt):
    url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e4df597e-4fc5-4a93-828b-77a6495511c2"
    data = {
        "msgtype":"markdown",
        "markdown": {
            "content": txt
            # "mentioned_list":["@all"],
            # "mentioned_mobile_list": ["@all"],
        }
    }
    response = requests.post( url, json=data)
    # print(response.status_code)
    # print(response.json())

def sendErrorMsg(apiType,txt):
	type_name = '文生图' if apiType == 'img' else '文生场景'
	for tag in ['<html>', '</html>', '<body>', '</body>', '<h1>', '</h1>','<p>','</p >']:
		txt = txt.replace(tag, '')
	txt = txt.replace('\n', ' ')
	# sendWxMsg("<font color=\"red\">" + f"{type_name}接口请求调用出现系统异常 :" + "</font>" + txt)
	# sendWxMsg(f"{BASE_URL}自动巡检：{type_name} 接口请求调用出现系统异常：\n <font color=\"red\">{txt}</font>")

def sort_and_sign_params(params, app_secret): 
    filtered_params = {k: v for k, v in params.items() if v is not None and v != '' and k != 'sign'} 
    def sort_and_format(param):  
        if isinstance(param, dict):  
            p = {k1: v1 for k1, v1 in param.items() if  v1 is not None and v1 != '' } 
            return '{' + '&'.join(  
                f'{k}={sort_and_format(v)}'  
                for k, v in sorted(p.items(), key=lambda item: item[0])  
            )   + '}'
        elif isinstance(param, list):  
            return '[' + ','.join(  
                sort_and_format(item) if isinstance(item, (dict, list)) else str(item)  
                for item in param  
            ) + ']'  
        else:  
            # return urllib.parse.quote(str(param))    
            return str(param).lower()   if isinstance(param,bool) else str(param)

    sorted_params = '&'.join(  
        f'{k}={sort_and_format(v)}'  
        for k, v in sorted(filtered_params.items(), key=lambda item: item[0])  
    )

    S2 = app_secret + sorted_params + app_secret  

    md5_hash = hashlib.md5(S2.encode('utf-8')).hexdigest()  
    return md5_hash  

def test_text2scene_command():
	data = {
		"query": "生成一个浪漫模式",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：主驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
			"sound_zone": 1,
			"vehicle_gear":1,
			"driver_seat_vent_value": 0,
			"inside_temp" : 23,
			"outside_temp":23,
			"current_speed": 0,
			"car_conf_str" : "111111111111111111111"
		},
		"image_scale": None,
		"intent_type": "command_control",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": 0.95,
		"llm_top_p": 0.9,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2scene_copilot():
    data = {
        "query": "生成一个浪漫模式",
        "appKey": appKey,
        "chat_id": str(uuid.uuid1()),
        "history": [],
        "car_info": {
            "complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：副驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
            "sound_zone": 1,
            "vehicle_gear":1,
            "driver_seat_vent_value": 0,
            "inside_temp" : 23,
            "outside_temp":23,
            "current_speed": 0,
            "car_conf_str" : "111111111111111111111"
        },
        "image_scale": None,
        "intent_type": "command_control",
        "task_type": 2,
        "test_web": True,
        "model_name": None,
        "need_system": False,
        "llm_temp": 0.95,
        "llm_top_p": 0.9,
        "skipBaiduCensor": True,
        "timestamp": int(time.time())
    }
    return data

def test_text2scene_vehicle_control():
	data = {
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"plugin_params": "{\"conf_word\":\"111111112221001111100\"}",
		"query": "AI备车",
		"car_info": {
			"current_speed": 0,
			"screen_width": 1920,
			"driver_seat_ac_value": 27,
			"singer": "张芸京",
			"sound_zone": 1,
			"album": "破天荒",
			"outside_temp": 10,
			"driver_seat_vent_value": 0,
			"passenger_seat_ac_value": 27,
			"day_night": 2,
			"screen_height": 984,
			"car_weather": "{\"code\":0,\"data\":{\"location\":{\"country\":\"中国\",\"name\":\"北京市\",\"province\":\"北京市\",\"timezone\":\"8\",\"name_zh\":\"beijingshi\",\"name_en\":\"Beijing\",\"id\":1,\"fcity_cn\":\"北京市\"},\"now\":{\"text\":\"晴\",\"humidity\":\"16\",\"icon\":\"http://static.adayotsp.com/weather2/W0.png\",\"pressure\":\"1024\",\"real_feel\":\"-3\",\"sun_rise\":\"2025-01-16 07:34:00\",\"sun_set\":\"2025-01-16 17:15:00\",\"temp\":\"2\",\"tips\":\"天气干冷，穿厚一点吧！\",\"update_time\":\"2025-01-16 08:15:08\",\"uvi\":\"4\",\"wind_dir\":\"北风\",\"wind_level\":\"3\",\"wind_speed\":\"3.5\"}},\"message\":\"ok\"}",
			"fragrance_ch2": 0,
			"fragrance_ch1": 0,
			"fragrance_ch3": 0,
			"inside_temp": 18,
			"car_conf_str": "111111112221001111100",
			"vehicle_gear": 1,
			"vehicle_name": "n51_HS7008A24072700151"
		},
		"image_scale": None,
		"intent_type": "vehicle_control",
		"task_type": 2,
		"test_web": True,
		"model_name": "Qwen2.5-32B-Instruct-GPTQ-Int4",
		"need_system": False,
		"llm_temp": 0.95,
		"llm_top_p": 0.9,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2image_kolors():
	data = {
		"query": "帮我画一幅过年的图片",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：主驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
			"sound_zone": 1,
			"vehicle_gear":1,
			"driver_seat_vent_value": 0,
			"inside_temp" : 23,
			"outside_temp":23,
			"current_speed": 0,
			"car_conf_str" : "111111111111111111111"
		},
		"image_scale": None,
		"intent_type": "monitor",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": None,
		"llm_top_p": None,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2image_aiauto():
	data = {
		"query": "大兴区,晴",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"current_speed": 61,
			"screen_width": 1920,
			"singer": "毕然",
			"driver_seat_ac_value": 24,
			"sound_zone": 1,
			"album": "如期",
			"outside_temp": 7,
			"driver_seat_vent_value": 0,
			"passenger_seat_ac_value": 24,
			"day_night": 2,
			"car_weather": "{\"code\":0,\"data\":{\"location\":{\"country\":\"中国\",\"name\":\"大兴区\",\"province\":\"北京市\",\"timezone\":\"8\",\"name_zh\":\"daxingqu\",\"name_en\":\"Daxing District\",\"id\":7,\"fcity_cn\":\"北京市\"},\"now\":{\"text\":\"晴\",\"humidity\":\"15\",\"icon\":\"http://static.adayotsp.com/weather2/W0.png\",\"pressure\":\"1026\",\"real_feel\":\"0\",\"sun_rise\":\"2025-01-16 07:33:00\",\"sun_set\":\"2025-01-16 17:15:00\",\"temp\":\"4\",\"tips\":\"天气干冷，穿厚一点吧！\",\"update_time\":\"2025-01-16 11:00:08\",\"uvi\":\"4\",\"wind_dir\":\"东北风\",\"wind_level\":\"2\",\"wind_speed\":\"3.19\"}},\"message\":\"ok\"}",
			"screen_height": 984,
			"fragrance_ch2": 0,
			"fragrance_ch1": 0,
			"fragrance_ch3": 0,
			"inside_temp": 9,
			"car_conf_str": "111111112221001111100",
			"vehicle_gear": 4,
			"vehicle_name": "n51_HS7008A24100800255"
		},
		"image_scale": None,
		"intent_type": "aiauto",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": None,
		"llm_top_p": None,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2image_car():
	data = {
		"query": "帮我画一幅极狐S5",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：主驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
			"sound_zone": 1,
			"vehicle_gear":1,
			"driver_seat_vent_value": 0,
			"inside_temp" : 23,
			"outside_temp":23,
			"current_speed": 0,
			"car_conf_str" : "111111111111111111111"
		},
		"image_scale": None,
		"intent_type": "command",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": None,
		"llm_top_p": None,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

class TestResult:
    def __init__(self):
        self.success_count = 0
        self.error_count = 0
        self.total_time = 0
        self.error_types = {}  # 添加错误类型统计
        self.response_times = []  # 保存所有响应时间

    def add_success(self, response_time):
        self.success_count += 1
        self.total_time += response_time
        self.response_times.append(response_time)

    def add_error(self, error):
        self.error_count += 1
        error_msg = str(error)
        self.error_types[error_msg] = self.error_types.get(error_msg, 0) + 1

    @property
    def avg_time(self):
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

    @property
    def max_time(self):
        return max(self.response_times) if self.response_times else 0

    @property
    def min_time(self):
        return min(self.response_times) if self.response_times else 0

    @property
    def percentile_90_time(self):
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.9)
        return sorted_times[index]

    @property
    def percentile_95_time(self):
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index]

    def get_qps(self, total_time):
        total_requests = self.success_count + self.error_count
        return total_requests / total_time if total_time > 0 else 0

    def get_time_distribution(self):
        if not self.response_times:
            return {}
            
        ranges = {
            '0-1秒': 0,
            '1-3秒': 0,
            '3-5秒': 0,
            '5-10秒': 0,
            '10-20秒': 0,
            '20秒以上': 0
        }
        
        for time in self.response_times:
            if time <= 1:
                ranges['0-1秒'] += 1
            elif time <= 3:
                ranges['1-3秒'] += 1
            elif time <= 5:
                ranges['3-5秒'] += 1
            elif time <= 10:
                ranges['5-10秒'] += 1
            elif time <= 20:
                ranges['10-20秒'] += 1
            else:
                ranges['20秒以上'] += 1
                
        return ranges

def generate_report(test_type, concurrent_num, result, total_time):
    # 先处理错误信息
    error_section = ""
    if result.errors:
        error_section = "#### 错误信息\n" + "\n".join(f"- {error}" for error in result.errors)
    
    report = f"""
### {test_type} 并发测试报告
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 并发数: {concurrent_num}
- 总执行时长: {total_time:.2f}秒

#### 测试结果
- 平均响应时间: {result.avg_time:.2f}秒
- 最大响应时间: {result.max_time:.2f}秒
- 最小响应时间: {result.min_time:.2f}秒
- 成功请求数: {result.success_count}
- 失败请求数: {result.error_count}

{error_section}
"""
    return report

async def async_test_request(url, data, timeout=30):
    """添加超时和网络监控的请求函数"""
    request_size = len(json.dumps(data).encode())
    network_start = time.time()
    
    try:
        async with aiohttp.ClientSession() as session:
            async with session.post(url, json=data, timeout=timeout) as response:
                network_delay = time.time() - network_start
                response_data = await response.json()
                response_size = len(json.dumps(response_data).encode())
                
                return {
                    'data': response_data,
                    'network_delay': network_delay,
                    'request_size': request_size,
                    'response_size': response_size
                }
    except asyncio.TimeoutError:
        raise Exception(f"请求超时(>{timeout}秒)")
    except Exception as e:
        raise e

async def async_test_text2scene(test_type):
    # 准备请求数据
    if test_type == "指令":
        data = test_text2scene_command()
    elif test_type == "副驾":
        data = test_text2scene_copilot()
    elif test_type == "AI备车":
        data = test_text2scene_vehicle_control()
    
    sign = sort_and_sign_params(data, appSecret)
    data["sign"] = sign
    
    start_time = time.time()
    try:
        response_data = await async_test_request(SCENE_URL, data)
        response_time = time.time() - start_time
        
        errorno = response_data.get('error_code')
        if errorno != 0:
            error_msg = response_data.get('error_message')
            raise Exception(f"业务错误: {error_msg}")
            
        return response_time
    except Exception as e:
        raise e

async def async_test_text2image(test_type):
    # 准备请求数据
    if test_type == "可图":
        data = test_text2image_kolors()
    elif test_type == "画车":
        data = test_text2image_car()
    elif test_type == "aiauto":
        data = test_text2image_aiauto()
    
    sign = sort_and_sign_params(data, appSecret)
    data["sign"] = sign
    
    start_time = time.time()
    try:
        response_data = await async_test_request(IMAGE_URL, data)
        response_time = time.time() - start_time
        
        errorno = response_data.get('errno')
        if errorno == -1:
            # 审核不通过也记为成功，因为这是正常的业务响应
            print(f"审核未通过: {response_data.get('data', {}).get('risk_message')}")
            return response_time
        elif errorno != 0:
            error_msg = response_data.get('msg')
            raise Exception(f"业务错误: {error_msg}")
            
        return response_time
    except Exception as e:
        raise e

async def concurrent_test(test_func, test_type, concurrent_num, duration=60):
    """
    @param concurrent_num: 并发数
    @param duration: 持续时间(秒)
    """
    result = TestResult()
    total_start_time = time.time()
    
    async def run_single_test(index):
        while time.time() - total_start_time < duration:
            try:
                start_time = time.time()
                response_time = await test_func(test_type)
                print(f"{test_type} 请求-{index} 完成: "
                      f"开始时间 {datetime.fromtimestamp(start_time).strftime('%H:%M:%S.%f')[:-3]}, "
                      f"耗时 {response_time:.2f}秒")
                result.add_success(response_time)
            except Exception as e:
                result.add_error(e)
                print(f"{test_type} 请求-{index} 失败: {str(e)}")
            # 随机等待100-500ms再发起下一次请求
            await asyncio.sleep(random.uniform(0.1, 0.5))
    
    tasks = [run_single_test(i) for i in range(concurrent_num)]
    await asyncio.gather(*tasks)
    
    total_time = time.time() - total_start_time
    return result, total_time

# 修改原有测试函数,添加并发支持
def test_text2scene(test_type, concurrent_num=1):
    if concurrent_num > 1:
        return concurrent_test(test_text2scene, test_type, concurrent_num)
        
    # 原有的单次测试逻辑
    current_time = datetime.now()
    print(f"\n{test_type} 执行时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    # 准备请求体
    if test_type == "指令":
        data = test_text2scene_command()
    elif test_type == "副驾":
        data = test_text2scene_copilot()
    elif test_type == "AI备车":
        data = test_text2scene_vehicle_control()
    sign = sort_and_sign_params(data,appSecret)
    data["sign"] =sign
    try:
        #connection = connect_PBDB()
        # 记录请求发送时间
        start_time = time.time()
        # print("请求: " + json.dumps(data))
        response = requests.post(SCENE_URL,  json=data)
        # 记录响应时间
        response_time = time.time() - start_time
        # 检查响应状态码
        status_code = response.status_code
        response.raise_for_status()
        response_data = response.json()
        # print("返回: " + json.dumps(response_data))
        errorno = response_data.get('error_code')
        print("status_code: " + str(status_code) + " errno: " + str(errorno))
        if errorno == 0:
            answer_data = response_data.get('observation').replace(r'```', '').replace('\n', '<br>')
        else:
            error_msg = response_data.get('error_message')
            sendErrorMsg("scene",test_type + error_msg)
            print("出现错误: " + error_msg)
            raise Exception(f"业务错误: {error_msg}")
        print(f"请求用时: {response_time:.2f} 秒")
    except Exception as err:
        sendErrorMsg("scene",test_type + str(err))
        print("出现错误: " + test_type + str(err))
        raise

def test_text2image(test_type, concurrent_num=1):
    if concurrent_num > 1:
        return concurrent_test(test_text2image, test_type, concurrent_num)
        
    # 原有的单次测试逻辑
    current_time = datetime.now()
    print(f"\n{test_type} 执行时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    # 准备请求体
    if test_type == "可图":
        data = test_text2image_kolors()
    elif test_type == "画车":
        data = test_text2image_car()
    elif test_type == "aiauto":
        data = test_text2image_aiauto()
    sign = sort_and_sign_params(data,appSecret)
    data["sign"] =sign
    try:
        #connection = connect_PBDB()
        # 记录请求发送时间
        start_time = time.time()
        # print("请求: " + json.dumps(data))
        response = requests.post(IMAGE_URL,  json=data)
        # 记录响应时间
        response_time = time.time() - start_time
        # 检查响应状态码
        status_code = response.status_code
        response.raise_for_status()
        response_data = response.json()
        # print("返回: " + json.dumps(response_data))
        errorno = response_data.get('errno')
        print("status_code: " + str(status_code) + " errno: " + str(errorno))
        if errorno == -1:
            #审核异常
            error_msg = response_data.get('data').get('risk_message')
            #sendErrorMsg("scene",test_type + response_data.get('msg'))
            print("出现错误: " + error_msg)
            raise Exception(f"审核错误: {error_msg}")
        elif errorno == 0:
            answer_data = response_data.get('data')
        else:
            error_msg = response_data.get('msg')
            sendErrorMsg("img",test_type + error_msg)
            print("出现错误: " + error_msg)
            raise Exception(f"业务错误: {error_msg}")
        print(f"请求用时: {response_time:.2f} 秒")
    except Exception as err:
        sendErrorMsg("scene",test_type + str(err))
        print("出现错误: " + test_type + str(err))
        raise

async def run_all_tests(concurrent_num):
    print(f"开始执行并发测试,并发数:{concurrent_num}")
    total_start_time = time.time()
    all_results = []
    
    test_scenarios = [
        (async_test_text2scene, "指令"),
        (async_test_text2scene, "副驾"),
        (async_test_text2scene, "AI备车"),
        (async_test_text2image, "可图"),
        (async_test_text2image, "画车"),
        (async_test_text2image, "aiauto")
    ]

    
    # 顺序执行每个场景，但场景内部是并发的
    for test_func, test_type in test_scenarios:
        print(f"\n开始执行 {test_type} 测试场景...")  # 添加场景开始的提示
        result, scenario_time = await concurrent_test(test_func, test_type, concurrent_num)
        print(f"{test_type} 测试场景完成，用时: {scenario_time:.2f}秒")  # 添加场景完成的提示
        all_results.append((test_type, result))
    
    total_time = time.time() - total_start_time
    
    # 生成汇总报告
    summary = f"""
# 并发测试汇总报告
- 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 并发数: {concurrent_num}
- 总测试时长: {total_time:.2f}秒

## 测试场景汇总
"""
    
    total_success = 0
    total_errors = 0
    for test_type, result in all_results:
        total_success += result.success_count
        total_errors += result.error_count
        summary += f"""
### {test_type}
- 平均响应时间: {result.avg_time:.2f}秒
- 成功数: {result.success_count}
- 失败数: {result.error_count}
"""
    
    summary += f"""
## 总体统计
- 总成功请求数: {total_success}
- 总失败请求数: {total_errors}
- 总请求数: {total_success + total_errors}
- 成功率: {(total_success/(total_success + total_errors)*100):.2f}%
- 总体QPS: {(total_success + total_errors)/total_time:.2f}
- 平均响应时间: {sum(r.avg_time * (r.success_count + r.error_count) for r in results.values())/(total_success + total_errors):.2f}秒
"""
    
    # 保存汇总报告
    report_dir = Path(__file__).parent / "test_reports"
    report_dir.mkdir(parents=True, exist_ok=True)
    summary_file = report_dir / f"summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    with open(summary_file, "w", encoding="utf-8") as f:
        f.write(summary)

class SessionMetrics:
    """会话指标统计"""
    def __init__(self, session_id):
        self.session_id = session_id
        self.start_time = time.time()
        self.request_count = 0
        self.error_count = 0
        self.last_active = time.time()
        self.scenario_history = []  # 场景转换历史
        self.think_times = []      # 思考时间记录
        self.response_times = []    # 响应时间记录
        self.is_locked = False      # 会话锁定状态
        
    def record_scenario(self, from_scenario, to_scenario):
        self.scenario_history.append({
            'from': from_scenario,
            'to': to_scenario,
            'time': time.time()
        })
        
    def record_think_time(self, think_time):
        self.think_times.append(think_time)
        
    def record_response(self, response_time):
        self.response_times.append(response_time)
        
    def get_stats(self):
        return {
            'uptime': time.time() - self.start_time,
            'request_count': self.request_count,
            'error_rate': self.error_count / self.request_count if self.request_count > 0 else 0,
            'avg_think_time': statistics.mean(self.think_times) if self.think_times else 0,
            'avg_response_time': statistics.mean(self.response_times) if self.response_times else 0,
            'scenario_transitions': len(self.scenario_history)
        }

class SessionManager:
    """会话管理器"""
    def __init__(self, target_sessions=54):
        self.target_sessions = target_sessions
        self.active_sessions = {}
        self.session_metrics = {}
        self.lock = asyncio.Lock()
        
    async def create_session(self, session_id):
        async with self.lock:
            if session_id not in self.active_sessions:
                self.active_sessions[session_id] = True
                self.session_metrics[session_id] = SessionMetrics(session_id)
                
    async def remove_session(self, session_id):
        async with self.lock:
            if session_id in self.active_sessions:
                del self.active_sessions[session_id]
                # 保留metrics用于分析
                
    async def check_sessions(self):
        """检查会话健康状态"""
        current_time = time.time()
        for session_id, metrics in self.session_metrics.items():
            if current_time - metrics.last_active > 30:  # 30秒无活动
                if not metrics.is_locked:
                    metrics.is_locked = True
                    await self.handle_inactive_session(session_id)
                    
    async def handle_inactive_session(self, session_id):
        """处理不活跃会话"""
        try:
            # 尝试恢复会话
            await self.remove_session(session_id)
            await self.create_session(session_id)
            self.session_metrics[session_id].is_locked = False
        except Exception as e:
            print(f"会话恢复失败 {session_id}: {str(e)}")

class UserSession:
    def __init__(self, user_id, session_manager):
        self.user_id = user_id
        self.session_manager = session_manager
        self.last_scenario = None
        self.consecutive_errors = 0
        
    async def think(self, scenario):
        think_time = random.uniform(*self.think_time[scenario])
        self.session_manager.session_metrics[self.user_id].record_think_time(think_time)
        await asyncio.sleep(think_time)
        
    def get_next_scenario(self, weights):
        current_scenario = self.last_scenario
        next_scenario = super().get_next_scenario(weights)
        
        # 记录场景转换
        if current_scenario:
            self.session_manager.session_metrics[self.user_id].record_scenario(
                current_scenario, next_scenario)
            
        return next_scenario

async def run_mixed_tests(concurrent_num, duration=60, weights=None):
    # 创建会话管理器
    session_manager = SessionManager(target_sessions=concurrent_num)
    
    # 创建监控器
    concurrency_monitor = ConcurrencyMonitor()
    network_monitor = NetworkMonitor()
    
    async def session_health_check():
        """会话健康检查协程"""
        while True:
            await session_manager.check_sessions()
            await asyncio.sleep(5)  # 每5秒检查一次
            
    async def run_user_session(user):
        await session_manager.create_session(user.user_id)
        
        try:
            while time.time() - total_start_time < duration:
                scenario = user.get_next_scenario(weights)
                test_func, test_type = scenario_map[scenario]
                
                try:
                    await user.think(scenario)
                    
                    concurrency_monitor.start_request(user.user_id)
                    start_time = time.time()
                    
                    response = await test_func(test_type)
                    response_time = time.time() - start_time
                    
                    # 记录统计信息
                    network_monitor.record_network_stats(
                        response['network_delay'],
                        response['request_size'],
                        response['response_size']
                    )
                    
                    session_manager.session_metrics[user.user_id].record_response(response_time)
                    
                    concurrency_monitor.end_request(user.user_id)
                    
                    print(f"用户{user.user_id} 执行 {scenario}: "
                          f"开始时间 {datetime.fromtimestamp(start_time).strftime('%H:%M:%S.%f')[:-3]}, "
                          f"总耗时 {response_time:.2f}秒, "
                          f"网络延迟 {response['network_delay']:.3f}秒")
                    
                except Exception as e:
                    session_manager.session_metrics[user.user_id].error_count += 1
                    if "请求超时" in str(e):
                        network_monitor.record_timeout()
                    await handle_session_error(user, e)
                    
        finally:
            await session_manager.remove_session(user.user_id)
    
    # 启动健康检查
    health_check_task = asyncio.create_task(session_health_check())
    
    # 创建用户会话任务
    session_tasks = [run_user_session(UserSession(i, session_manager)) 
                    for i in range(concurrent_num)]
    
    # 等待所有任务完成
    await asyncio.gather(*session_tasks)
    health_check_task.cancel()
    
    # 生成详细报告
    generate_detailed_report(
        session_manager.session_metrics,
        concurrency_monitor.get_stats(),
        network_monitor.get_stats()
    )

def generate_detailed_report(session_metrics, concurrency_stats, network_stats):
    """生成详细的测试报告"""
    current_time = datetime.now().strftime('%Y-%m-%d %H:%M:%S')
    
    # 1. 会话统计
    session_summary = []
    total_requests = 0
    total_errors = 0
    total_think_time = 0
    total_response_time = 0
    
    for session_id, metrics in session_metrics.items():
        stats = metrics.get_stats()
        session_summary.append({
            'session_id': session_id,
            'uptime': stats['uptime'],
            'requests': stats['request_count'],
            'error_rate': stats['error_rate'],
            'avg_think_time': stats['avg_think_time'],
            'avg_response_time': stats['avg_response_time'],
            'transitions': stats['scenario_transitions']
        })
        total_requests += stats['request_count']
        total_errors += stats['request_count'] * stats['error_rate']
        total_think_time += stats['avg_think_time']
        total_response_time += stats['avg_response_time']
    
    # 2. 生成报告内容
    report = f"""
# 54并发用户测试详细报告
测试时间: {current_time}

## 1. 总体统计
- 活跃会话数: {len(session_metrics)}
- 总请求数: {total_requests}
- 平均错误率: {(total_errors/total_requests*100):.2f}% 
- 平均思考时间: {(total_think_time/len(session_metrics)):.2f}秒
- 平均响应时间: {(total_response_time/len(session_metrics)):.2f}秒

## 2. 并发统计
- 平均并发数: {concurrency_stats['avg_concurrent']:.2f}
- 最大并发数: {concurrency_stats['max_concurrent']}
- 最小并发数: {concurrency_stats['min_concurrent']}
- 总请求数: {concurrency_stats['total_requests']}
- 平均QPS: {concurrency_stats['qps']:.2f}

## 3. 网络性能
- 平均网络延迟: {network_stats['avg_delay']:.3f}秒
- 最大网络延迟: {network_stats['max_delay']:.3f}秒
- 最小网络延迟: {network_stats['min_delay']:.3f}秒
- 90%网络延迟: {network_stats['p90_delay']:.3f}秒
- 平均请求大小: {network_stats['avg_request_size']/1024:.2f}KB
- 平均响应大小: {network_stats['avg_response_size']/1024:.2f}KB
- 超时次数: {network_stats['timeouts']}次

## 4. 会话详情
"""
    
    # 添加每个会话的详细信息
    for session in session_summary:
        report += f"""
### 会话 {session['session_id']}
- 运行时长: {session['uptime']:.2f}秒
- 请求数: {session['requests']}
- 错误率: {session['error_rate']*100:.2f}%
- 平均思考时间: {session['avg_think_time']:.2f}秒
- 平均响应时间: {session['avg_response_time']:.2f}秒
- 场景转换次数: {session['transitions']}
"""
    
    # 保存报告
    report_dir = Path(__file__).parent / "test_reports"
    report_dir.mkdir(parents=True, exist_ok=True)
    report_file = report_dir / f"detailed_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(report)
        
    print(f"详细报告已生成: {report_file}")

if __name__ == "__main__":
    # 测试配置
    concurrent_num = 54  # 并发用户数
    duration = 60       # 测试时长(秒)
    
    # 自定义场景权重
    weights = {
        '指令': 0.005,      # 0.5%
        '副驾': 0.0025,     # 0.25%
        'AI备车': 0.48,     # 48%
        '可图': 0.01,       # 1%
        '画车': 0.0025,     # 0.25%
        'aiauto': 0.48      # 48%
    }
    
    # 执行混合场景测试
    asyncio.run(run_mixed_tests(
        concurrent_num=concurrent_num,
        duration=duration,      # 指定测试时长
        weights=weights
    ))
