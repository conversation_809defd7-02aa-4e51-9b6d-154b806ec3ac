@echo off
setlocal enabledelayedexpansion

REM 默认参数
set PORT=7860
set HOST=0.0.0.0
set SHARE=
set GPU_ID=0

REM 解析命令行参数
:parse_args
if "%~1"=="" goto :end_parse
if "%~1"=="--port" (
    set PORT=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--listen" (
    set HOST=%~2
    shift
    shift
    goto :parse_args
)
if "%~1"=="--share" (
    set SHARE=--share
    shift
    goto :parse_args
)
if "%~1"=="--gpu_id" (
    set GPU_ID=%~2
    shift
    shift
    goto :parse_args
)
echo 未知参数: %~1
exit /b 1

:end_parse

REM 安装依赖
pip install -r requirements.txt

REM 启动UI
python app.py --port %PORT% --listen %HOST% %SHARE% --gpu_id %GPU_ID%

endlocal 