import requests
import json
import time
import uuid
import hashlib
import datetime
import concurrent.futures
from datetime import datetime
import statistics
from pathlib import Path
import aiohttp
import asyncio
import random
#from mysql_util import connect_PBDB,update_data,insert_data_and_get_id,execute_query,insert_data,insert_data_id_not_exists

TEST_BASE_URL = "106.63.5.45:19099"
BASE_URL = "203.176.93.140:19099"
IMAGE_URL = f"http://{BASE_URL}/lm/text2image"
SCENE_URL = f"http://{BASE_URL}/lm/text2scene"
appKey = 'bgju38949K5J6e54NSgh831'
appSecret = 'aT9034u5jFd8u3k5djk4tPg89ojhNu8wos'

def sendWxMsg(txt):
    url = "https://qyapi.weixin.qq.com/cgi-bin/webhook/send?key=e4df597e-4fc5-4a93-828b-77a6495511c2"
    data = {
        "msgtype":"markdown",
        "markdown": {
            "content": txt
            # "mentioned_list":["@all"],
            # "mentioned_mobile_list": ["@all"],
        }
    }
    response = requests.post( url, json=data)
    # print(response.status_code)
    # print(response.json())

def sendErrorMsg(apiType,txt):
	type_name = '文生图' if apiType == 'img' else '文生场景'
	for tag in ['<html>', '</html>', '<body>', '</body>', '<h1>', '</h1>','<p>','</p >']:
		txt = txt.replace(tag, '')
	txt = txt.replace('\n', ' ')
	# sendWxMsg("<font color=\"red\">" + f"{type_name}接口请求调用出现系统异常 :" + "</font>" + txt)
	# sendWxMsg(f"{BASE_URL}自动巡检：{type_name} 接口请求调用出现系统异常：\n <font color=\"red\">{txt}</font>")

def sort_and_sign_params(params, app_secret): 
    filtered_params = {k: v for k, v in params.items() if v is not None and v != '' and k != 'sign'} 
    def sort_and_format(param):  
        if isinstance(param, dict):  
            p = {k1: v1 for k1, v1 in param.items() if  v1 is not None and v1 != '' } 
            return '{' + '&'.join(  
                f'{k}={sort_and_format(v)}'  
                for k, v in sorted(p.items(), key=lambda item: item[0])  
            )   + '}'
        elif isinstance(param, list):  
            return '[' + ','.join(  
                sort_and_format(item) if isinstance(item, (dict, list)) else str(item)  
                for item in param  
            ) + ']'  
        else:  
            # return urllib.parse.quote(str(param))    
            return str(param).lower()   if isinstance(param,bool) else str(param)

    sorted_params = '&'.join(  
        f'{k}={sort_and_format(v)}'  
        for k, v in sorted(filtered_params.items(), key=lambda item: item[0])  
    )

    S2 = app_secret + sorted_params + app_secret  

    md5_hash = hashlib.md5(S2.encode('utf-8')).hexdigest()  
    return md5_hash  

def test_text2scene_command():
	data = {
		"query": "生成一个浪漫模式",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：主驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
			"sound_zone": 1,
			"vehicle_gear":1,
			"driver_seat_vent_value": 0,
			"inside_temp" : 23,
			"outside_temp":23,
			"current_speed": 0,
			"car_conf_str" : "111111111111111111111"
		},
		"image_scale": None,
		"intent_type": "command_control",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": 0.95,
		"llm_top_p": 0.9,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2scene_copilot():
    data = {
        "query": "生成一个浪漫模式",
        "appKey": appKey,
        "chat_id": str(uuid.uuid1()),
        "history": [],
        "car_info": {
            "complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：副驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
            "sound_zone": 1,
            "vehicle_gear":1,
            "driver_seat_vent_value": 0,
            "inside_temp" : 23,
            "outside_temp":23,
            "current_speed": 0,
            "car_conf_str" : "111111111111111111111"
        },
        "image_scale": None,
        "intent_type": "command_control",
        "task_type": 2,
        "test_web": True,
        "model_name": None,
        "need_system": False,
        "llm_temp": 0.95,
        "llm_top_p": 0.9,
        "skipBaiduCensor": True,
        "timestamp": int(time.time())
    }
    return data

def test_text2scene_vehicle_control():
	data = {
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"plugin_params": "{\"conf_word\":\"111111112221001111100\"}",
		"query": "AI备车",
		"car_info": {
			"current_speed": 0,
			"screen_width": 1920,
			"driver_seat_ac_value": 27,
			"singer": "张芸京",
			"sound_zone": 1,
			"album": "破天荒",
			"outside_temp": 10,
			"driver_seat_vent_value": 0,
			"passenger_seat_ac_value": 27,
			"day_night": 2,
			"screen_height": 984,
			"car_weather": "{\"code\":0,\"data\":{\"location\":{\"country\":\"中国\",\"name\":\"北京市\",\"province\":\"北京市\",\"timezone\":\"8\",\"name_zh\":\"beijingshi\",\"name_en\":\"Beijing\",\"id\":1,\"fcity_cn\":\"北京市\"},\"now\":{\"text\":\"晴\",\"humidity\":\"16\",\"icon\":\"http://static.adayotsp.com/weather2/W0.png\",\"pressure\":\"1024\",\"real_feel\":\"-3\",\"sun_rise\":\"2025-01-16 07:34:00\",\"sun_set\":\"2025-01-16 17:15:00\",\"temp\":\"2\",\"tips\":\"天气干冷，穿厚一点吧！\",\"update_time\":\"2025-01-16 08:15:08\",\"uvi\":\"4\",\"wind_dir\":\"北风\",\"wind_level\":\"3\",\"wind_speed\":\"3.5\"}},\"message\":\"ok\"}",
			"fragrance_ch2": 0,
			"fragrance_ch1": 0,
			"fragrance_ch3": 0,
			"inside_temp": 18,
			"car_conf_str": "111111112221001111100",
			"vehicle_gear": 1,
			"vehicle_name": "n51_HS7008A24072700151"
		},
		"image_scale": None,
		"intent_type": "vehicle_control",
		"task_type": 2,
		"test_web": True,
		"model_name": "Qwen2.5-32B-Instruct-GPTQ-Int4",
		"need_system": False,
		"llm_temp": 0.95,
		"llm_top_p": 0.9,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2image_kolors():
	data = {
		"query": "帮我画一幅过年的图片",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：主驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
			"sound_zone": 1,
			"vehicle_gear":1,
			"driver_seat_vent_value": 0,
			"inside_temp" : 23,
			"outside_temp":23,
			"current_speed": 0,
			"car_conf_str" : "111111111111111111111"
		},
		"image_scale": None,
		"intent_type": "monitor",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": None,
		"llm_top_p": None,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2image_aiauto():
	data = {
		"query": "大兴区,晴",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"current_speed": 61,
			"screen_width": 1920,
			"singer": "毕然",
			"driver_seat_ac_value": 24,
			"sound_zone": 1,
			"album": "如期",
			"outside_temp": 7,
			"driver_seat_vent_value": 0,
			"passenger_seat_ac_value": 24,
			"day_night": 2,
			"car_weather": "{\"code\":0,\"data\":{\"location\":{\"country\":\"中国\",\"name\":\"大兴区\",\"province\":\"北京市\",\"timezone\":\"8\",\"name_zh\":\"daxingqu\",\"name_en\":\"Daxing District\",\"id\":7,\"fcity_cn\":\"北京市\"},\"now\":{\"text\":\"晴\",\"humidity\":\"15\",\"icon\":\"http://static.adayotsp.com/weather2/W0.png\",\"pressure\":\"1026\",\"real_feel\":\"0\",\"sun_rise\":\"2025-01-16 07:33:00\",\"sun_set\":\"2025-01-16 17:15:00\",\"temp\":\"4\",\"tips\":\"天气干冷，穿厚一点吧！\",\"update_time\":\"2025-01-16 11:00:08\",\"uvi\":\"4\",\"wind_dir\":\"东北风\",\"wind_level\":\"2\",\"wind_speed\":\"3.19\"}},\"message\":\"ok\"}",
			"screen_height": 984,
			"fragrance_ch2": 0,
			"fragrance_ch1": 0,
			"fragrance_ch3": 0,
			"inside_temp": 9,
			"car_conf_str": "111111112221001111100",
			"vehicle_gear": 4,
			"vehicle_name": "n51_HS7008A24100800255"
		},
		"image_scale": None,
		"intent_type": "aiauto",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": None,
		"llm_top_p": None,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

def test_text2image_car():
	data = {
		"query": "帮我画一幅极狐S5",
		"appKey": appKey,
		"chat_id": str(uuid.uuid1()),
		"history": [],
		"car_info": {
			"complete_vehicle_condition": "播放的歌名：无\\n音乐的歌手：无\\n请求人位置：主驾\\n当前车内温度：23\\n当前车外温度：23\\n当前车辆档位：P\\n当前车辆速度：0\\n当前主驾空调温度：\\n当前副驾空调温度：\\n当前空调风量：0",
			"sound_zone": 1,
			"vehicle_gear":1,
			"driver_seat_vent_value": 0,
			"inside_temp" : 23,
			"outside_temp":23,
			"current_speed": 0,
			"car_conf_str" : "111111111111111111111"
		},
		"image_scale": None,
		"intent_type": "command",
		"task_type": 2,
		"test_web": True,
		"model_name": None,
		"need_system": False,
		"llm_temp": None,
		"llm_top_p": None,
		"skipBaiduCensor": True,
		"timestamp": int(time.time())
	}
	return data

class TestResult:
    def __init__(self):
        self.success_count = 0
        self.error_count = 0
        self.total_time = 0
        self.error_types = {}  # 添加错误类型统计
        self.response_times = []  # 保存所有响应时间

    def add_success(self, response_time):
        self.success_count += 1
        self.total_time += response_time
        self.response_times.append(response_time)

    def add_error(self, error):
        self.error_count += 1
        error_msg = str(error)
        self.error_types[error_msg] = self.error_types.get(error_msg, 0) + 1

    @property
    def avg_time(self):
        if not self.response_times:
            return 0.0
        return sum(self.response_times) / len(self.response_times)

    @property
    def max_time(self):
        return max(self.response_times) if self.response_times else 0

    @property
    def min_time(self):
        return min(self.response_times) if self.response_times else 0

    @property
    def percentile_90_time(self):
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.9)
        return sorted_times[index]

    @property
    def percentile_95_time(self):
        if not self.response_times:
            return 0.0
        sorted_times = sorted(self.response_times)
        index = int(len(sorted_times) * 0.95)
        return sorted_times[index]

    def get_qps(self, total_time):
        total_requests = self.success_count + self.error_count
        return total_requests / total_time if total_time > 0 else 0

    def get_time_distribution(self):
        if not self.response_times:
            return {}
            
        ranges = {
            '0-1秒': 0,
            '1-3秒': 0,
            '3-5秒': 0,
            '5-10秒': 0,
            '10-20秒': 0,
            '20秒以上': 0
        }
        
        for time in self.response_times:
            if time <= 1:
                ranges['0-1秒'] += 1
            elif time <= 3:
                ranges['1-3秒'] += 1
            elif time <= 5:
                ranges['3-5秒'] += 1
            elif time <= 10:
                ranges['5-10秒'] += 1
            elif time <= 20:
                ranges['10-20秒'] += 1
            else:
                ranges['20秒以上'] += 1
                
        return ranges

def generate_report(results, qps_monitor, total_time, weights, start_time):
    """生成更详细的测试报告"""
    qps_stats = qps_monitor.get_stats()
    total_success = sum(r.success_count for r in results.values())
    total_errors = sum(r.error_count for r in results.values())
    
    summary = f"""# 混合场景并发测试报告

## 测试基本信息
- 测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}
- 测试结束时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 总执行时长: {total_time:.2f}秒
- 目标并发数: {concurrent_per_second}
- 实际总请求数: {total_success + total_errors}

## 总体性能指标
- 平均QPS: {qps_stats['avg_qps']:.2f}
- 峰值QPS: {qps_stats['peak_qps']}
- 最低QPS: {qps_stats['min_qps']}
- 总成功率: {(total_success/(total_success + total_errors)*100):.2f}%
- 总错误数: {total_errors}

## 响应时间分布
"""
    # 添加响应时间分布统计
    for scenario_name, result in results.items():
        if result.response_times:
            time_ranges = result.get_time_distribution()
            summary += f"\n### {scenario_name} 响应时间分布\n"
            for range_name, count in time_ranges.items():
                percentage = count / len(result.response_times) * 100
                summary += f"- {range_name}: {count}次 ({percentage:.2f}%)\n"

    summary += "\n## 场景执行详情\n"
    for scenario_name, result in results.items():
        total_requests = result.success_count + result.error_count
        if total_requests > 0:
            success_rate = result.success_count / total_requests * 100
            summary += f"""
### {scenario_name}
- 目标权重: {weights[scenario_name]*100:.2f}%
- 实际请求数: {total_requests}
- 实际权重: {(total_requests/(total_success + total_errors)*100):.2f}%
- 成功请求: {result.success_count}
- 失败请求: {result.error_count}
- 成功率: {success_rate:.2f}%
- 响应时间:
  - 平均: {result.avg_time:.2f}秒
  - 最小: {result.min_time:.2f}秒
  - 最大: {result.max_time:.2f}秒
  - 90%线: {result.percentile_90_time:.2f}秒
  - 95%线: {result.percentile_95_time:.2f}秒
"""
            if result.error_types:
                summary += "- 错误统计:\n"
                for error_msg, count in result.error_types.items():
                    summary += f"  - {error_msg}: {count}次\n"

    summary += "\n## QPS趋势\n"
    for record in qps_stats['qps_history']:
        timestamp = datetime.fromtimestamp(record['timestamp']).strftime('%H:%M:%S')
        summary += f"- {timestamp}: 总QPS {record['qps']}\n"
        for scenario, qps in record['scenario_qps'].items():
            summary += f"  - {scenario}: {qps} QPS\n"

    summary += """
## 请求数量说明
- 每秒22个请求 × 30秒 = 660个总请求
- 每个场景每秒至少执行1次，确保场景覆盖
- 剩余请求按权重分配

### 理论请求数（按权重）
"""
    total_requests = concurrent_per_second * duration
    for name, weight in weights.items():
        theoretical = weight * total_requests
        summary += f"- {name}: {weight*100:.2f}% -> 约{int(theoretical)}个请求\n"
    
    summary += "\n### 实际请求数（考虑最小执行次数）\n"
    for name, result in results.items():
        actual = result.success_count + result.error_count
        summary += f"- {name}: {actual}个请求\n"

    return summary

async def async_test_request(url, data):
    async with aiohttp.ClientSession() as session:
        async with session.post(url, json=data) as response:
            return await response.json()

async def async_test_text2scene(test_type):
    # 准备请求数据
    if test_type == "指令":
        data = test_text2scene_command()
    elif test_type == "副驾":
        data = test_text2scene_copilot()
    elif test_type == "AI备车":
        data = test_text2scene_vehicle_control()
    
    sign = sort_and_sign_params(data, appSecret)
    data["sign"] = sign
    
    start_time = time.time()
    try:
        response_data = await async_test_request(SCENE_URL, data)
        response_time = time.time() - start_time
        
        errorno = response_data.get('error_code')
        if errorno != 0:
            error_msg = response_data.get('error_message')
            raise Exception(f"业务错误: {error_msg}")
            
        return response_time
    except Exception as e:
        raise e

async def async_test_text2image(test_type):
    # 准备请求数据
    if test_type == "可图":
        data = test_text2image_kolors()
    elif test_type == "画车":
        data = test_text2image_car()
    elif test_type == "aiauto":
        data = test_text2image_aiauto()
    
    sign = sort_and_sign_params(data, appSecret)
    data["sign"] = sign
    
    start_time = time.time()
    try:
        response_data = await async_test_request(IMAGE_URL, data)
        response_time = time.time() - start_time
        
        errorno = response_data.get('errno')
        if errorno == -1:
            # 审核不通过也记为成功，因为这是正常的业务响应
            print(f"审核未通过: {response_data.get('data', {}).get('risk_message')}")
            return response_time
        elif errorno != 0:
            error_msg = response_data.get('msg')
            raise Exception(f"业务错误: {error_msg}")
            
        return response_time
    except Exception as e:
        raise e

async def concurrent_test(test_func, test_type, concurrent_num, duration=60):
    """
    @param concurrent_num: 并发数
    @param duration: 持续时间(秒)
    """
    result = TestResult()
    total_start_time = time.time()
    
    async def run_single_test(index):
        while time.time() - total_start_time < duration:
            try:
                start_time = time.time()
                response_time = await test_func(test_type)
                print(f"{test_type} 请求-{index} 完成: "
                      f"开始时间 {datetime.fromtimestamp(start_time).strftime('%H:%M:%S.%f')[:-3]}, "
                      f"耗时 {response_time:.2f}秒")
                result.add_success(response_time)
            except Exception as e:
                result.add_error(e)
                print(f"{test_type} 请求-{index} 失败: {str(e)}")
            # 随机等待100-500ms再发起下一次请求
            await asyncio.sleep(random.uniform(0.1, 0.5))
    
    tasks = [run_single_test(i) for i in range(concurrent_num)]
    await asyncio.gather(*tasks)
    
    total_time = time.time() - total_start_time
    return result, total_time

# 修改原有测试函数,添加并发支持
def test_text2scene(test_type, concurrent_num=1):
    if concurrent_num > 1:
        return concurrent_test(test_text2scene, test_type, concurrent_num)
        
    # 原有的单次测试逻辑
    current_time = datetime.now()
    print(f"\n{test_type} 执行时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    # 准备请求体
    if test_type == "指令":
        data = test_text2scene_command()
    elif test_type == "副驾":
        data = test_text2scene_copilot()
    elif test_type == "AI备车":
        data = test_text2scene_vehicle_control()
    sign = sort_and_sign_params(data,appSecret)
    data["sign"] =sign
    try:
        #connection = connect_PBDB()
        # 记录请求发送时间
        start_time = time.time()
        # print("请求: " + json.dumps(data))
        response = requests.post(SCENE_URL,  json=data)
        # 记录响应时间
        response_time = time.time() - start_time
        # 检查响应状态码
        status_code = response.status_code
        response.raise_for_status()
        response_data = response.json()
        # print("返回: " + json.dumps(response_data))
        errorno = response_data.get('error_code')
        print("status_code: " + str(status_code) + " errno: " + str(errorno))
        if errorno == 0:
            answer_data = response_data.get('observation').replace(r'```', '').replace('\n', '<br>')
        else:
            error_msg = response_data.get('error_message')
            sendErrorMsg("scene",test_type + error_msg)
            print("出现错误: " + error_msg)
            raise Exception(f"业务错误: {error_msg}")
        print(f"请求用时: {response_time:.2f} 秒")
    except Exception as err:
        sendErrorMsg("scene",test_type + str(err))
        print("出现错误: " + test_type + str(err))
        raise

def test_text2image(test_type, concurrent_num=1):
    if concurrent_num > 1:
        return concurrent_test(test_text2image, test_type, concurrent_num)
        
    # 原有的单次测试逻辑
    current_time = datetime.now()
    print(f"\n{test_type} 执行时间: {current_time.strftime('%Y-%m-%d %H:%M:%S')}")
    # 准备请求体
    if test_type == "可图":
        data = test_text2image_kolors()
    elif test_type == "画车":
        data = test_text2image_car()
    elif test_type == "aiauto":
        data = test_text2image_aiauto()
    sign = sort_and_sign_params(data,appSecret)
    data["sign"] =sign
    try:
        #connection = connect_PBDB()
        # 记录请求发送时间
        start_time = time.time()
        # print("请求: " + json.dumps(data))
        response = requests.post(IMAGE_URL,  json=data)
        # 记录响应时间
        response_time = time.time() - start_time
        # 检查响应状态码
        status_code = response.status_code
        response.raise_for_status()
        response_data = response.json()
        # print("返回: " + json.dumps(response_data))
        errorno = response_data.get('errno')
        print("status_code: " + str(status_code) + " errno: " + str(errorno))
        if errorno == -1:
            #审核异常
            error_msg = response_data.get('data').get('risk_message')
            #sendErrorMsg("scene",test_type + response_data.get('msg'))
            print("出现错误: " + error_msg)
            raise Exception(f"审核错误: {error_msg}")
        elif errorno == 0:
            answer_data = response_data.get('data')
        else:
            error_msg = response_data.get('msg')
            sendErrorMsg("img",test_type + error_msg)
            print("出现错误: " + error_msg)
            raise Exception(f"业务错误: {error_msg}")
        print(f"请求用时: {response_time:.2f} 秒")
    except Exception as err:
        sendErrorMsg("scene",test_type + str(err))
        print("出现错误: " + test_type + str(err))
        raise

def save_execution_log(execution_log, start_time):
    """保存执行日志"""
    log_dir = Path(__file__).parent / "test_reports" / "logs"
    log_dir.mkdir(parents=True, exist_ok=True)
    
    # 创建详细日志文件
    detailed_log_file = log_dir / f"detailed_log_{start_time.strftime('%Y%m%d_%H%M%S')}.log"
    summary_log_file = log_dir / f"summary_log_{start_time.strftime('%Y%m%d_%H%M%S')}.log"
    
    # 分离详细日志和汇总日志
    detailed_logs = []
    summary_logs = []
    
    for log in execution_log:
        if log.startswith("\n第"):  # 场景分配信息
            summary_logs.append(log)
        else:  # 请求执行详情
            detailed_logs.append(log)
    
    # 保存详细日志
    with open(detailed_log_file, "w", encoding="utf-8") as f:
        f.write("# 请求执行详细日志\n")
        f.write(f"测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("\n".join(detailed_logs))
    
    # 保存汇总日志
    with open(summary_log_file, "w", encoding="utf-8") as f:
        f.write("# 场景分配汇总日志\n")
        f.write(f"测试开始时间: {start_time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write("\n".join(summary_logs))
    
    return detailed_log_file, summary_log_file

async def run_mixed_tests(concurrent_per_second=54, duration=60, weights=None):
    """执行混合场景压力测试"""
    start_time = datetime.now()
    execution_log = []  # 用于收集执行日志
    
    print(f"开始执行混合场景并发测试,每秒并发数:{concurrent_per_second}")
    
    # 创建并发控制器
    controller = ConcurrencyController(concurrent_per_second)
    controller.start()
    
    # 创建QPS监控器
    qps_monitor = QPSMonitor()
    
    # 默认权重配置
    if weights is None:
        weights = {
            '指令': 0.005,      # 0.5%
            '副驾': 0.0025,     # 0.25%
            'AI备车': 0.48,     # 48%
            '可图': 0.01,       # 1%
            '画车': 0.0025,     # 0.25%
            'aiauto': 0.48      # 48%
        }
    
    # 场景映射
    scenario_map = {
        '指令': (async_test_text2scene, "指令"),
        '副驾': (async_test_text2scene, "副驾"),
        'AI备车': (async_test_text2scene, "AI备车"),
        '可图': (async_test_text2image, "可图"),
        '画车': (async_test_text2image, "画车"),
        'aiauto': (async_test_text2image, "aiauto")
    }
    
    # 结果统计
    results = {name: TestResult() for name in weights.keys()}
    total_start_time = time.time()
    
    async def execute_request(test_func, test_type, scenario_name):
        """执行单个请求"""
        try:
            await controller.control_qps()
            
            start_time = time.time()
            response_time = await test_func(test_type)
            
            # 记录QPS
            qps_monitor.record_request(scenario_name)
            
            # 格式化日志信息
            log_entry = (f"执行 {scenario_name}: "
                        f"开始时间 {datetime.fromtimestamp(start_time).strftime('%H:%M:%S.%f')[:-3]}, "
                        f"耗时 {response_time:.2f}秒, "
                        f"当前QPS: {controller.requests_this_second}")
            
            print(log_entry)
            execution_log.append(log_entry)  # 添加到执行日志列表
                    
            results[scenario_name].add_success(response_time)
            
        except Exception as e:
            error_log = f"{scenario_name} 失败: {str(e)}"
            print(error_log)
            execution_log.append(error_log)  # 记录错误信息
            results[scenario_name].add_error(e)
    
    def get_actual_requests():
        """计算每秒实际的请求分配"""
        actual_requests = {name: 1 for name in weights.keys()}  # 确保每个场景至少1个请求
        remaining = concurrent_per_second - len(weights)  # 减去已分配的最小请求数
        
        if remaining <= 0:
            print("警告: 并发数小于场景数，每个场景只能执行一次")
            return actual_requests
            
        # 计算剩余请求的分配
        base_requests = {
            name: weight * remaining
            for name, weight in weights.items()
        }
        
        # 处理整数部分
        for name, count in base_requests.items():
            integer_part = int(count)
            actual_requests[name] += integer_part
            remaining -= integer_part
            
        # 处理小数部分
        decimal_parts = {
            name: count - int(count)
            for name, count in base_requests.items()
        }
        
        # 按小数部分大小排序，优先分配给小数部分大的场景
        sorted_decimals = sorted(
            decimal_parts.items(),
            key=lambda x: x[1],
            reverse=True
        )
        
        # 分配剩余的请求
        for name, _ in sorted_decimals:
            if remaining > 0:
                actual_requests[name] += 1
                remaining -= 1
            else:
                break
                
        # 如果还有剩余，分配给权重最大的场景
        if remaining > 0:
            max_weight_scenario = max(weights.items(), key=lambda x: x[1])[0]
            actual_requests[max_weight_scenario] += remaining
            
        return actual_requests
    
    # 按秒执行测试
    for second in range(duration):
        second_start = time.time()
        
        # 获取这一秒的请求分配
        scenario_counts = get_actual_requests()
        
        print(f"\n第 {second+1} 秒场景分配:")
        total_allocated = sum(scenario_counts.values())
        for name, count in scenario_counts.items():
            actual_weight = count / total_allocated * 100
            target_weight = weights[name] * 100
            print(f"- {name}: {count}个请求 "
                  f"(目标权重{target_weight:.2f}%, "
                  f"实际权重{actual_weight:.2f}%)")
        
        # 记录场景分配
        log_entry = f"\n第 {second+1} 秒场景分配:\n"
        for name, count in scenario_counts.items():
            actual_weight = count / total_allocated * 100
            target_weight = weights[name] * 100
            log_entry += f"- {name}: {count}个请求 (目标权重{target_weight:.2f}%, 实际权重{actual_weight:.2f}%)\n"
        execution_log.append(log_entry)
        
        # 创建该秒的所有请求任务
        tasks = []
        for scenario_name, count in scenario_counts.items():
            test_func, test_type = scenario_map[scenario_name]
            for _ in range(count):
                tasks.append(execute_request(test_func, test_type, scenario_name))
        
        # 同时执行所有请求
        await asyncio.gather(*tasks)
        
        # 确保每秒的执行时间精确
        elapsed = time.time() - second_start
        if elapsed < 1:
            await asyncio.sleep(1 - elapsed)
    
    # 生成报告
    total_time = time.time() - total_start_time
    
    # 生成汇总报告
    summary = generate_report(results, qps_monitor, total_time, weights, start_time)
    
    # 保存报告
    report_dir = Path(__file__).parent / "test_reports"
    report_dir.mkdir(parents=True, exist_ok=True)
    report_file = report_dir / f"mixed_summary_{datetime.now().strftime('%Y%m%d_%H%M%S')}.md"
    
    with open(report_file, "w", encoding="utf-8") as f:
        f.write(summary)
        
    print(f"\n测试完成，报告已保存至: {report_file}")
    
    # 保存执行日志
    detailed_log_file, summary_log_file = save_execution_log(execution_log, start_time)
    print(f"\n详细执行日志已保存至: {detailed_log_file}")
    print(f"场景分配汇总日志已保存至: {summary_log_file}")
    
    return results, total_time

class ConcurrencyController:
    """并发控制器"""
    def __init__(self, target_qps):
        self.target_qps = target_qps
        self.current_second = 0
        self.request_count = 0
        self.start_time = None
        self.requests_this_second = 0
        
    def start(self):
        self.start_time = time.time()
        self.current_second = 0
        
    async def control_qps(self):
        """控制QPS"""
        current_time = time.time()
        elapsed_seconds = int(current_time - self.start_time)
        
        if elapsed_seconds > self.current_second:
            # 新的一秒开始
            self.current_second = elapsed_seconds
            self.requests_this_second = 0
            
        if self.requests_this_second >= self.target_qps:
            # 当前秒的请求数已达到目标，等待下一秒
            wait_time = (self.current_second + 1) - (current_time - self.start_time)
            if wait_time > 0:
                await asyncio.sleep(wait_time)
                
        self.requests_this_second += 1
        self.request_count += 1

class QPSMonitor:
    """QPS监控器"""
    def __init__(self):
        self.qps_history = []  # 记录每秒的QPS
        self.current_second = 0
        self.start_time = time.time()
        self.request_count = 0
        self.peak_qps = 0
        self.scenario_qps = {}  # 记录每个场景的QPS
        
    def record_request(self, scenario_name):
        current_time = time.time()
        elapsed_seconds = int(current_time - self.start_time)
        
        if elapsed_seconds > self.current_second:
            # 记录上一秒的QPS
            if self.request_count > 0:
                qps = self.request_count
                self.peak_qps = max(self.peak_qps, qps)
                self.qps_history.append({
                    'timestamp': current_time,
                    'qps': qps,
                    'scenario_qps': self.scenario_qps.copy()
                })
            
            # 重置计数器
            self.current_second = elapsed_seconds
            self.request_count = 0
            self.scenario_qps = {}
            
        self.request_count += 1
        self.scenario_qps[scenario_name] = self.scenario_qps.get(scenario_name, 0) + 1
        
    def get_stats(self):
        if not self.qps_history:
            return {
                'avg_qps': 0,
                'peak_qps': 0,
                'min_qps': 0,
                'qps_history': [],
                'scenario_avg_qps': {}
            }
            
        qps_values = [record['qps'] for record in self.qps_history]
        scenario_total = {}
        for record in self.qps_history:
            for scenario, qps in record['scenario_qps'].items():
                scenario_total[scenario] = scenario_total.get(scenario, 0) + qps
                
        total_seconds = len(self.qps_history)
        scenario_avg_qps = {
            scenario: count/total_seconds 
            for scenario, count in scenario_total.items()
        }
        
        return {
            'avg_qps': statistics.mean(qps_values),
            'peak_qps': self.peak_qps,
            'min_qps': min(qps_values),
            'qps_history': self.qps_history,
            'scenario_avg_qps': scenario_avg_qps
        }

if __name__ == "__main__":
    # 测试配置
    concurrent_per_second = 80  # 每秒并发请求数
    duration = 5              # 测试时长(秒)
    
    # 自定义场景权重
    weights = {
        '指令': 0.005,      # 0.5%
        '副驾': 0.0025,     # 0.25%
        'AI备车': 0.48,     # 48%
        '可图': 0.01,       # 1%
        '画车': 0.0025,     # 0.25%
        'aiauto': 0.48      # 48%
    }
    
    # 执行混合场景测试
    asyncio.run(run_mixed_tests(
        concurrent_per_second=concurrent_per_second,
        duration=duration,
        weights=weights
    ))
