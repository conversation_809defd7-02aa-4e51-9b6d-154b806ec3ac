import re
import argparse
from datasets import Dataset
import json
from unsloth import FastLanguageModel
from unsloth import is_bfloat16_supported
from vllm import SamplingParams

# 定义模型权重路径
model_weight_paths = {
    "origin": "/models/Qwen2.5-1.5B-Instruct",
    "distill": "/models/DeepSeek-R1-Distill-Qwen-1.5B"
}

# 系统提示词
RL_SYSTEM_PROMPT = """
Respond in the following format:
<think>
推理过程
</think>
最终答案
"""

SFT_SYSTEM_PROMPT = """
You are a helpful assistant.
The assistant first thinks about the reasoning process in the mind and then provides the user with the answer.
The reasoning process enclosed within <think> </think> tags, and answer after </think> tag, respectively, i.e., <think> reasoning process here </think> answer here.
"""

SFT_EMPTY_SYSTEM_PROMPT = """
You are a helpful assistant.
"""


def eval(args):
    """
    根据命令行参数训练语言模型。
    """
    # 加载模型和分词器
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name = model_weight_paths[args.train_mode] if args.model_path is None else args.model_path,
        max_seq_length = args.max_seq_length,
        load_in_4bit = True, # False for LoRA 16bit
        fast_inference = True, # Enable vLLM fast inference
        max_lora_rank = args.lora_rank,
        # gpu_memory_utilization = gpu_memory_utilization, # Reduce if out of memory
    )

    # 根据args配置，选择系统提示词
    messages = []
    if args.type == "rl" and args.train_mode == "orgin":
        messages.append({'role': 'system', 'content': RL_SYSTEM_PROMPT})
    elif args.type == 'sft' and args.train_mode == "origin" and args.system_message == "empty":
        messages.append({'role': 'system', 'content': SFT_EMPTY_SYSTEM_PROMPT})
    elif args.type == 'sft' and args.train_mode == "origin" and args.system_message == "instruction":
        messages.append({'role': 'system', 'content': SFT_SYSTEM_PROMPT})
    messages.append({'role': 'user', 'content': args.question})

    # 使用chat_template拼接信息
    text = tokenizer.apply_chat_template(
        messages, tokenize = False, add_generation_prompt = True)

    # 定义采样参数
    sampling_params = SamplingParams(
        temperature = 0.8,
        top_p = 0.95,
        max_tokens = 1024,
    )

    if not args.model_path:
        # 初始化lora模型
        model = FastLanguageModel.get_peft_model(
            model,
            r = args.lora_rank, # Choose any number > 0 ! Suggested 8, 16, 32, 64, 128
            target_modules = [
                # "q_proj", "k_proj", "v_proj", "o_proj",
                "gate_proj", "up_proj", "down_proj",
            ], # Remove QKVO if out of memory
            lora_alpha = args.lora_alpha,
            use_gradient_checkpointing = "unsloth", # Enable long context finetuning
            random_state = 3407,
        )    

        # 获取输出
        output = model.fast_generate(
            text,
            sampling_params = sampling_params,
            lora_request = model.load_lora(args.lora_dir) if args.lora_dir is not None else None,
        )[0].outputs[0].text
        print(output)
    else:
        output = model.fast_generate(
            text,
            sampling_params = sampling_params,
        )[0].outputs[0].text
        print(output)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Evaluate a language model with specified parameters.")
    parser.add_argument("--train_mode", type=str, choices=["origin", "distill"], default="origin", help="Training mode: origin or distill.")
    parser.add_argument("--model_path", type=str, default=None, help="Directory for model weights, if not none, default model path will disabled and do not use lora.")
    parser.add_argument("--type", type=str, choices=["rl", "sft"], default="sft", help="Type of training: rl or sft.")
    parser.add_argument("--system_message", type=str, choices=["empty", "instruction"], default="instruction", help="System message type for SFT: empty or instruction.")
    parser.add_argument("--question", type=str, required=True, help="Question to be answered by the model.")
    parser.add_argument("--max_seq_length", type=int, default=2048, help="Maximum sequence length for the model.")
    parser.add_argument("--lora_rank", type=int, default=8, help="LoRA rank for model fine-tuning.")
    parser.add_argument("--lora_alpha", type=int, default=16, help="LoRA alpha for model fine-tuning.")
    parser.add_argument("--lora_dir", type=str, default=None, help="Directory for LoRA weights.")
    
    args = parser.parse_args()
    eval(args)

