import os
import time
from PIL import Image
import pillow_heif
from pathlib import Path

# 注册 HEIF 格式支持
pillow_heif.register_heif_opener()

class ImageWebCompressor:
    """
    图像web格式压缩转换器
    支持多种输入格式转换为web优化格式：WebP、AVIF、优化JPG/PNG
    """
    
    def __init__(self):
        # 支持的输入格式
        self.supported_formats = {'.png', '.jpg', '.jpeg', '.bmp', '.tiff', '.tif', '.webp', '.heic', '.heif'}
        
        # 输出格式配置
        self.output_formats = {
            'webp': {
                'extension': '.webp',
                'format': 'WEBP',
                'quality_range': (10, 100),
                'default_quality': 80,
                'supports_alpha': True
            },
            'avif': {
                'extension': '.avif',
                'format': 'AVIF', 
                'quality_range': (10, 100),
                'default_quality': 75,
                'supports_alpha': True
            },
            'jpg': {
                'extension': '.jpg',
                'format': 'JPEG',
                'quality_range': (10, 100),
                'default_quality': 85,
                'supports_alpha': False
            },
            'png': {
                'extension': '.png',
                'format': 'PNG',
                'quality_range': (0, 9),  # PNG压缩级别
                'default_quality': 6,
                'supports_alpha': True
            }
        }

    def validate_paths(self, source_dir, dest_dir):
        """验证路径"""
        if not os.path.exists(source_dir):
            raise ValueError(f"源目录不存在: {source_dir}")
        
        if not os.path.exists(dest_dir):
            os.makedirs(dest_dir)
            print(f"创建目标目录: {dest_dir}")

    def get_files_to_convert(self, source_dir):
        """获取需要转换的文件列表"""
        files = []
        for filename in os.listdir(source_dir):
            file_path = os.path.join(source_dir, filename)
            if os.path.isfile(file_path):
                file_ext = Path(filename).suffix.lower()
                if file_ext in self.supported_formats:
                    files.append(filename)
        return files

    def calculate_compression_ratio(self, original_size, compressed_size):
        """计算压缩比"""
        if original_size == 0:
            return 0
        return round((1 - compressed_size / original_size) * 100, 2)

    def convert_single_image(self, source_path, dest_path, output_format, quality=None, max_width=None, max_height=None):
        """转换单个图像"""
        try:
            format_config = self.output_formats[output_format]
            
            # 获取原始文件大小
            original_size = os.path.getsize(source_path)
            
            with Image.open(source_path) as img:
                # 获取原始尺寸
                original_width, original_height = img.size
                
                # 尺寸调整
                if max_width or max_height:
                    img = self.resize_image(img, max_width, max_height)
                
                # 处理透明度
                if not format_config['supports_alpha'] and img.mode in ('RGBA', 'LA'):
                    # 创建白色背景
                    background = Image.new('RGB', img.size, (255, 255, 255))
                    if img.mode == 'RGBA':
                        background.paste(img, mask=img.split()[-1])
                    else:
                        background.paste(img)
                    img = background
                elif format_config['supports_alpha'] and img.mode not in ('RGBA', 'RGB'):
                    img = img.convert('RGBA' if img.mode in ('RGBA', 'LA') else 'RGB')
                
                # 设置质量参数
                if quality is None:
                    quality = format_config['default_quality']
                
                save_kwargs = {}
                
                if output_format == 'png':
                    # PNG使用压缩级别
                    save_kwargs = {
                        'format': format_config['format'],
                        'compress_level': quality,
                        'optimize': True
                    }
                elif output_format in ['webp', 'avif', 'jpg']:
                    save_kwargs = {
                        'format': format_config['format'],
                        'quality': quality,
                        'optimize': True
                    }
                    
                    if output_format == 'webp':
                        save_kwargs['method'] = 6  # 更好的压缩
                    elif output_format == 'jpg':
                        save_kwargs['progressive'] = True
                
                # 保存图像
                img.save(dest_path, **save_kwargs)
                
                # 获取压缩后文件大小
                compressed_size = os.path.getsize(dest_path)
                compression_ratio = self.calculate_compression_ratio(original_size, compressed_size)
                
                return {
                    'success': True,
                    'original_size': original_size,
                    'compressed_size': compressed_size,
                    'compression_ratio': compression_ratio,
                    'original_dimensions': (original_width, original_height),
                    'final_dimensions': img.size
                }
                
        except Exception as e:
            return {
                'success': False,
                'error': str(e)
            }

    def resize_image(self, img, max_width=None, max_height=None):
        """调整图像尺寸，保持宽高比"""
        width, height = img.size
        
        # 计算新尺寸
        if max_width and max_height:
            # 按比例缩放到指定尺寸内
            ratio = min(max_width / width, max_height / height)
        elif max_width:
            ratio = max_width / width
        elif max_height:
            ratio = max_height / height
        else:
            return img
        
        if ratio < 1:  # 只有需要缩小时才调整
            new_width = int(width * ratio)
            new_height = int(height * ratio)
            return img.resize((new_width, new_height), Image.Resampling.LANCZOS)
        
        return img

    def batch_convert(self, source_dir, dest_dir, output_format='webp', quality=None, 
                     max_width=None, max_height=None, keep_original_names=False):
        """批量转换图像"""
        
        # 验证参数
        if output_format not in self.output_formats:
            raise ValueError(f"不支持的输出格式: {output_format}")
        
        # 验证路径
        self.validate_paths(source_dir, dest_dir)
        
        # 获取文件列表
        files = self.get_files_to_convert(source_dir)
        
        if not files:
            print("没有找到支持的图像文件")
            return
        
        print(f"找到 {len(files)} 个文件待转换")
        print(f"输出格式: {output_format.upper()}")
        print(f"质量设置: {quality or self.output_formats[output_format]['default_quality']}")
        if max_width or max_height:
            print(f"尺寸限制: {max_width or '不限'}x{max_height or '不限'}")
        print("-" * 50)
        
        # 统计信息
        total_original_size = 0
        total_compressed_size = 0
        success_count = 0
        failed_count = 0
        
        # 逐个转换
        for i, filename in enumerate(files, 1):
            source_path = os.path.join(source_dir, filename)
            
            # 生成目标文件名
            if keep_original_names:
                base_name = Path(filename).stem
                dest_filename = f"{base_name}{self.output_formats[output_format]['extension']}"
            else:
                timestamp = int(time.time() * 1000) + i  # 避免重名
                dest_filename = f"{timestamp}{self.output_formats[output_format]['extension']}"
            
            dest_path = os.path.join(dest_dir, dest_filename)
            
            # 转换图像
            result = self.convert_single_image(
                source_path, dest_path, output_format, quality, max_width, max_height
            )
            
            if result['success']:
                success_count += 1
                total_original_size += result['original_size']
                total_compressed_size += result['compressed_size']
                
                print(f"[{i}/{len(files)}] ✅ {filename} -> {dest_filename}")
                print(f"    尺寸: {result['original_dimensions']} -> {result['final_dimensions']}")
                print(f"    大小: {self.format_file_size(result['original_size'])} -> {self.format_file_size(result['compressed_size'])} "
                      f"(压缩 {result['compression_ratio']}%)")
            else:
                failed_count += 1
                print(f"[{i}/{len(files)}] ❌ {filename} 转换失败: {result['error']}")
        
        # 输出总结
        print("-" * 50)
        print(f"转换完成！")
        print(f"成功: {success_count} 个文件")
        print(f"失败: {failed_count} 个文件")
        
        if success_count > 0:
            total_compression = self.calculate_compression_ratio(total_original_size, total_compressed_size)
            print(f"总体压缩比: {total_compression}%")
            print(f"节省空间: {self.format_file_size(total_original_size - total_compressed_size)}")

    def format_file_size(self, size_bytes):
        """格式化文件大小显示"""
        if size_bytes < 1024:
            return f"{size_bytes} B"
        elif size_bytes < 1024 * 1024:
            return f"{size_bytes / 1024:.1f} KB"
        else:
            return f"{size_bytes / (1024 * 1024):.1f} MB"


def main():
    """主函数 - 使用示例"""
    compressor = ImageWebCompressor()
    
    # 配置参数
    source_directory = r"D:\下载\png"  # 源图像目录
    destination_directory = r"D:\下载\web_images"  # 输出目录
    
    # 转换选项
    output_format = 'webp'  # 输出格式: webp, avif, jpg, png
    quality = 80  # 质量 (WebP/AVIF/JPG: 10-100, PNG: 0-9)
    max_width = 1920  # 最大宽度 (None表示不限制)
    max_height = 1080  # 最大高度 (None表示不限制)
    keep_original_names = True  # 是否保持原文件名
    
    try:
        print("🚀 图像Web格式压缩转换器")
        print(f"源目录: {source_directory}")
        print(f"目标目录: {destination_directory}")
        print("=" * 60)
        
        compressor.batch_convert(
            source_dir=source_directory,
            dest_dir=destination_directory,
            output_format=output_format,
            quality=quality,
            max_width=max_width,
            max_height=max_height,
            keep_original_names=keep_original_names
        )
        
    except Exception as e:
        print(f"❌ 程序执行出错: {e}")


if __name__ == "__main__":
    main() 