<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Camera Interaction App</title>
    <style>
        body {
            font-family: sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            gap: 20px;
            padding: 20px;
            background-color: #f0f0f0;
        }
        .controls, .io-areas {
            display: flex;
            gap: 10px;
            align-items: center;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .io-areas {
            flex-direction: column;
            align-items: stretch;
        }
        textarea {
            width: 300px;
            height: 80px;
            padding: 8px;
            border: 1px solid #ccc;
            border-radius: 4px;
            font-size: 14px;
        }
        #videoFeed {
            display: none; /* 隐藏原始视频 */
        }
        #displayCanvas {
            width: 480px;
            height: 360px;
            border: 2px solid #333;
            background-color: #000;
            border-radius: 8px;
        }
        #startButton {
            padding: 10px 20px;
            font-size: 16px;
            cursor: pointer;
            border: none;
            border-radius: 4px;
            color: white;
        }
        #startButton.start {
            background-color: #28a745; /* Green */
        }
        #startButton.stop {
            background-color: #dc3545; /* Red */
        }
        label {
            font-weight: bold;
        }
        select {
            padding: 8px;
            border-radius: 4px;
            border: 1px solid #ccc;
        }
        .hidden {
            display: none;
        }
        .blur-controls {
            display: flex;
            align-items: center;
            gap: 10px;
            background-color: #fff;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        .slider-container {
            display: flex;
            flex-direction: column;
            gap: 5px;
        }
        .slider-value {
            text-align: center;
            font-size: 14px;
        }
    </style>
</head>
<body>

    <h1>Camera Interaction App</h1>

    <video id="videoFeed" autoplay playsinline></video>
    <canvas id="displayCanvas"></canvas> <!-- 用于显示模糊效果 -->
    <canvas id="canvas" class="hidden"></canvas> <!-- 用于捕获原始帧 -->
    <canvas id="blurCanvas" class="hidden"></canvas> <!-- 用于模糊处理 -->

    <div class="blur-controls">
        <div class="slider-container">
            <label for="blurSlider">模糊程度:</label>
            <input type="range" id="blurSlider" min="0" max="30" value="0" step="1">
            <div class="slider-value" id="blurValue">0 px</div>
        </div>
        <div>
            <label for="blurMethod">模糊方式:</label>
            <select id="blurMethod">
                <option value="gaussian">高斯模糊</option>
                <option value="pixelate">像素化</option>
                <option value="none">无</option>
            </select>
        </div>
    </div>

    <div class="io-areas">
        <div>
            <label for="baseURL">Base API:</label><br>
            <input id="baseURL" name="Instruction" value="http://27.159.93.61:8198"></textarea>
        </div>
        <div>
            <label for="instructionText">Instruction:</label><br>
            <textarea id="instructionText" style="height: 2em; width: 40em" name="Instruction"></textarea>
        </div>
        <div>
            <label for="responseText">Response:</label><br>
            <textarea id="responseText" style="height: 2em; width: 40em" name="Response" readonly placeholder="Server response will appear here..."></textarea>
        </div>
    </div>

    <div class="controls">
        <label for="intervalSelect">Interval between 2 requests:</label>
        <select id="intervalSelect" name="Interval between 2 requests">
            <option value="100">100ms</option>
            <option value="250">250ms</option>
            <option value="500" selected>500ms</option>
            <option value="1000">1s</option>
            <option value="2000">2s</option>
            <option value="5000">5s</option>
            <option value="10000">10s</option>
        </select>
        <button id="startButton" class="start">Start</button>
    </div>

    <script>
        const video = document.getElementById('videoFeed');
        const displayCanvas = document.getElementById('displayCanvas');
        const canvas = document.getElementById('canvas');
        const blurCanvas = document.getElementById('blurCanvas');
        const baseURL = document.getElementById('baseURL');
        const instructionText = document.getElementById('instructionText');
        const responseText = document.getElementById('responseText');
        const intervalSelect = document.getElementById('intervalSelect');
        const startButton = document.getElementById('startButton');
        const blurSlider = document.getElementById('blurSlider');
        const blurValue = document.getElementById('blurValue');
        const blurMethod = document.getElementById('blurMethod');

        instructionText.value = "What do you see?"; // default instruction

        let stream;
        let intervalId;
        let isProcessing = false;
        let blurAmount = 0;
        let currentBlurMethod = 'gaussian';
        let displayIntervalId; // 用于更新显示的定时器ID

        // Update blur value display
        blurSlider.addEventListener('input', () => {
            blurAmount = parseInt(blurSlider.value);
            blurValue.textContent = `${blurAmount} px`;
        });

        // Update blur method
        blurMethod.addEventListener('change', () => {
            currentBlurMethod = blurMethod.value;
        });

        // Returns response text (string)
        async function sendChatCompletionRequest(instruction, imageBase64URL) {
            const response = await fetch(`${baseURL.value}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    max_tokens: 100,
                    messages: [
                        { role: 'user', content: [
                            { type: 'text', text: instruction },
                            { type: 'image_url', image_url: {
                                url: imageBase64URL,
                            } }
                        ] },
                    ]
                })
            });
            if (!response.ok) {
                const errorData = await response.text();
                return `Server error: ${response.status} - ${errorData}`;
            }
            const data = await response.json();
            return data.choices[0].message.content;
        }

        // 1. Ask for camera permission on load
        async function initCamera() {
            try {
                stream = await navigator.mediaDevices.getUserMedia({ video: true, audio: false });
                video.srcObject = stream;
                responseText.value = "Camera access granted. Ready to start.";
                
                // 初始化显示画布尺寸
                // 摄像头准备好后才能获取正确的视频尺寸
                video.addEventListener('loadedmetadata', () => {
                    displayCanvas.width = video.videoWidth;
                    displayCanvas.height = video.videoHeight;
                    // 启动显示更新循环
                    updateDisplay();
                });
            } catch (err) {
                console.error("Error accessing camera:", err);
                responseText.value = `Error accessing camera: ${err.name} - ${err.message}. Please ensure permissions are granted and you are on HTTPS or localhost.`;
                alert(`Error accessing camera: ${err.name}. Make sure you've granted permission and are on HTTPS or localhost.`);
            }
        }

        // Apply Gaussian blur effect
        function applyGaussianBlur(context, canvas, radius) {
            if (radius <= 0) return;
            
            // 使用多次小半径模糊以获得更好的高斯模糊效果
            const iterations = 3;
            const actualRadius = Math.floor(radius / iterations);
            
            // 应用多次盒子模糊来近似高斯模糊
            for (let i = 0; i < iterations; i++) {
                // 水平模糊
                const tempCanvas = document.createElement('canvas');
                tempCanvas.width = canvas.width;
                tempCanvas.height = canvas.height;
                const tempContext = tempCanvas.getContext('2d');
                tempContext.drawImage(canvas, 0, 0);
                
                context.clearRect(0, 0, canvas.width, canvas.height);
                context.filter = `blur(${actualRadius}px)`;
                context.drawImage(tempCanvas, 0, 0);
                context.filter = 'none';
            }
        }

        // Apply pixelation effect
        function applyPixelation(context, canvas, blockSize) {
            if (blockSize <= 1) return;
            
            const w = canvas.width;
            const h = canvas.height;
            
            // 获取原始图像数据
            const imgData = context.getImageData(0, 0, w, h);
            const data = imgData.data;
            
            // 将图像分成blockSize*blockSize大小的块
            for (let y = 0; y < h; y += blockSize) {
                for (let x = 0; x < w; x += blockSize) {
                    // 计算每个块的区域
                    const blockWidth = Math.min(blockSize, w - x);
                    const blockHeight = Math.min(blockSize, h - y);
                    
                    // 计算块的平均颜色
                    let r = 0, g = 0, b = 0, a = 0, count = 0;
                    
                    for (let by = 0; by < blockHeight; by++) {
                        for (let bx = 0; bx < blockWidth; bx++) {
                            const i = ((y + by) * w + (x + bx)) * 4;
                            r += data[i];
                            g += data[i + 1];
                            b += data[i + 2];
                            a += data[i + 3];
                            count++;
                        }
                    }
                    
                    // 平均颜色
                    r = Math.floor(r / count);
                    g = Math.floor(g / count);
                    b = Math.floor(b / count);
                    a = Math.floor(a / count);
                    
                    // 填充块区域为平均颜色
                    for (let by = 0; by < blockHeight; by++) {
                        for (let bx = 0; bx < blockWidth; bx++) {
                            const i = ((y + by) * w + (x + bx)) * 4;
                            data[i] = r;
                            data[i + 1] = g;
                            data[i + 2] = b;
                            data[i + 3] = a;
                        }
                    }
                }
            }
            
            // 将处理后的图像数据放回画布
            context.putImageData(imgData, 0, 0);
        }

        // 捕获并返回原始图像（用于发送给模型）
        function captureOriginalImage() {
            if (!stream || !video.videoWidth) {
                console.warn("Video stream not ready for capture.");
                return null;
            }
            
            const width = video.videoWidth;
            const height = video.videoHeight;
            
            // 设置画布尺寸
            canvas.width = width;
            canvas.height = height;
            
            // 绘制到临时画布
            const context = canvas.getContext('2d');
            context.drawImage(video, 0, 0, width, height);
            
            // 返回原始图像
            return canvas.toDataURL('image/jpeg', 0.8);
        }

        // 更新显示画布（模糊效果）
        function updateDisplay() {
            if (!stream || !video.videoWidth) return;
            
            const width = video.videoWidth;
            const height = video.videoHeight;
            
            // 确保显示画布大小正确
            if (displayCanvas.width !== width) displayCanvas.width = width;
            if (displayCanvas.height !== height) displayCanvas.height = height;
            
            // 准备模糊画布
            blurCanvas.width = width;
            blurCanvas.height = height;
            
            // 绘制到原始画布
            const context = blurCanvas.getContext('2d');
            context.drawImage(video, 0, 0, width, height);
            
            // 应用模糊效果
            if (blurAmount > 0 && currentBlurMethod !== 'none') {
                if (currentBlurMethod === 'gaussian') {
                    applyGaussianBlur(context, blurCanvas, blurAmount);
                } else if (currentBlurMethod === 'pixelate') {
                    applyPixelation(context, blurCanvas, blurAmount);
                }
            }
            
            // 将结果绘制到显示画布
            const displayContext = displayCanvas.getContext('2d');
            displayContext.drawImage(blurCanvas, 0, 0);
            
            // 持续更新显示
            if (stream) {
                requestAnimationFrame(updateDisplay);
            }
        }

        async function sendData() {
            if (!isProcessing) return; // Ensure we don't have overlapping requests if processing takes longer than interval

            const instruction = instructionText.value;
            const imageBase64URL = captureOriginalImage(); // 使用原始图像，而不是模糊后的

            if (!imageBase64URL) {
                responseText.value = "Failed to capture image. Stream might not be active.";
                // Optionally stop processing if image capture fails consistently
                // handleStop();
                return;
            }

            const payload = {
                instruction: instruction,
                imageBase64URL: imageBase64URL
            };

            try {
                const response = await sendChatCompletionRequest(payload.instruction, payload.imageBase64URL);
                // 只有在处理中才更新响应文本，防止停止后仍然更新
                if (isProcessing) {
                    responseText.value = response;
                }
            } catch (error) {
                console.error('Error sending data:', error);
                if (isProcessing) {
                    responseText.value = `Error: ${error.message}`;
                }
            }
        }

        function handleStart() {
            if (!stream) {
                responseText.value = "Camera not available. Cannot start.";
                alert("Camera not available. Please grant permission first.");
                return;
            }
            isProcessing = true;
            startButton.textContent = "Stop";
            startButton.classList.remove('start');
            startButton.classList.add('stop');

            instructionText.disabled = true;
            intervalSelect.disabled = true;

            responseText.value = "Processing started...";

            const intervalMs = parseInt(intervalSelect.value, 10);
            
            // Initial immediate call
            sendData(); 
            
            // Then set interval
            intervalId = setInterval(sendData, intervalMs);
        }

        function handleStop() {
            isProcessing = false;
            if (intervalId) {
                clearInterval(intervalId);
                intervalId = null;
            }
            startButton.textContent = "Start";
            startButton.classList.remove('stop');
            startButton.classList.add('start');

            instructionText.disabled = false;
            intervalSelect.disabled = false;
            responseText.value = "处理已停止。";
        }

        startButton.addEventListener('click', () => {
            if (isProcessing) {
                handleStop();
            } else {
                handleStart();
            }
        });

        // Initialize camera when the page loads
        window.addEventListener('DOMContentLoaded', initCamera);

        // Optional: Stop stream when page is closed/navigated away to release camera
        window.addEventListener('beforeunload', () => {
            if (stream) {
                stream.getTracks().forEach(track => track.stop());
            }
            if (intervalId) {
                clearInterval(intervalId);
            }
        });

    </script>
</body>
</html>