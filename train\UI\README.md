# DeepSeek训练系统UI设计文档

## 技术栈

- **前端框架**：Gradio
- **后端支持**：Python
- **可视化工具**：Plotly、Matplotlib
- **训练框架**：Unsloth

## 功能概述

DeepSeek训练系统UI提供了一个直观、全面的界面，用于管理和监控大语言模型的训练过程。系统支持SFT（监督微调）、RL（强化学习）和DPO（直接偏好优化）三种主要训练方式，并提供实时训练监控和结果评估。

## 主要界面布局

### 1. 主控制面板

```
+----------------------------------------------------------+
|                    DeepSeek 训练系统                      |
+----------------------------------------------------------+
|  [模型选择] [ 训练类型 ] [ 数据管理 ] [ 训练监控 ] [ 评估 ]  |
+----------------------------------------------------------+
|                                                          |
|                     内容显示区域                          |
|                                                          |
+----------------------------------------------------------+
```

### 2. 模型选择与配置

- **模型类型选择**：下拉菜单选择基础模型（Qwen2.5-1.5B、Qwen2.5-7B等）
- **训练模式**：Origin模式、Distill模式
- **系统提示词**：自定义系统提示词编辑区
- **LoRA参数配置**
  - Rank值调整（滑块：4-128）
  - Alpha值设置（滑块：1-32）
  - Target Modules选择（多选框）

### 3. 训练数据管理

- **数据导入**：支持上传JSON/JSONL/Excel格式数据
- **数据预览**：表格形式展示数据样本
- **数据处理**：
  - 数据分割比例调整
  - 数据格式验证
  - 数据增强选项
- **数据导出**：处理后数据导出功能

### 4. 训练配置面板

#### 4.1 SFT训练配置

- **批量大小**：滑块（1-64）
- **学习率**：输入框（默认2e-4）
- **训练轮次**：滑块（1-50）
- **格式化函数**：选择formatting_prompts_func
- **混合精度**：FP16/BF16选择
- **梯度累积**：步数设置

#### 4.2 RL训练配置

- **批量大小**：滑块（1-16）
- **学习率**：输入框（默认1e-5）
- **最大步数**：输入框（默认2000）
- **奖励函数配置**：
  - 格式正确性奖励权重
  - 语义相似度奖励权重
  - 推理质量奖励权重
- **训练参数**：
  - Beta值设置
  - KL惩罚系数

#### 4.3 DPO训练配置

- **批量大小**：滑块设置
- **学习率**：输入框
- **Beta值**：滑块（0.1-10）
- **数据预处理选项**

### 5. 训练监控

- **实时损失曲线**：使用Plotly绘制训练损失变化
- **学习率变化**：学习率调整可视化
- **GPU资源监控**：
  - 显存使用率
  - GPU利用率
  - 温度监控
- **训练速度**：每秒处理样本数/每步时间
- **训练进度**：进度条与剩余时间估计
- **检查点保存**：自动/手动保存检查点选项

### 6. 模型评估

- **在线推理测试**：输入问题，获取模型回答
- **批量评估**：上传测试集进行批量评估
- **评估指标**：
  - 格式正确率
  - 语义相似度分数
  - 推理质量评分
- **结果比较**：不同训练阶段模型结果对比
- **结果导出**：评估报告导出功能

### 7. 部署设置

- **模型导出**：
  - 导出合并后的模型
  - 导出LoRA权重
- **量化选项**：
  - INT4/INT8量化
  - GGUF转换
- **推理加速**：
  - Flash Attention启用
  - vLLM集成设置

## 界面展示示例

### 训练监控界面

```
+----------------------------------------------------------+
|                    实时训练监控                           |
+----------------------------------------------------------+
|                                                          |
|    [损失曲线图]                 [资源使用率]              |
|                                                          |
|    训练进度: [==========>      ] 46%                     |
|    当前步数: 920/2000                                    |
|    当前损失: 0.342                                       |
|    学习率: 9.8e-6                                        |
|    每步时间: 2.3秒                                       |
|    预估剩余: 41分钟                                      |
|                                                          |
|    [暂停训练]  [恢复训练]  [保存检查点]  [终止训练]       |
+----------------------------------------------------------+
```

### 评估界面

```
+----------------------------------------------------------+
|                    模型评估                              |
+----------------------------------------------------------+
|                                                          |
|  问题输入:                                               |
|  [                                           ]           |
|                                                          |
|  [提交问题]                                              |
|                                                          |
|  模型回答:                                               |
|  +----------------------------------------------------+  |
|  | <think>                                           |  |
|  | 这里是模型的思考过程...                            |  |
|  | </think>                                          |  |
|  | 这里是模型的最终回答...                            |  |
|  +----------------------------------------------------+  |
|                                                          |
|  评分:                                                   |
|  格式正确性: 1.0                                         |
|  语义相似度: 0.87                                        |
|  推理质量: 0.92                                          |
|                                                          |
+----------------------------------------------------------+
```

## 实现计划

### 第一阶段: 基础功能实现

1. 创建基本Gradio界面框架
2. 实现模型选择与配置功能
3. 开发数据管理模块
4. 构建SFT训练配置界面

### 第二阶段: 高级功能实现

1. 开发RL训练配置界面
2. 实现DPO训练配置界面
3. 构建训练监控系统
4. 开发评估界面

### 第三阶段: 可视化与优化

1. 实现各类可视化图表
2. 优化UI交互体验
3. 集成GPU资源监控
4. 增加高级配置选项

## 技术实现细节

### 后端架构

系统将采用模块化设计，主要包括：

1. **数据处理模块**：负责数据导入、验证、分割
2. **训练控制模块**：管理训练进程与参数
3. **监控模块**：收集并处理训练指标
4. **评估模块**：执行模型推理与评分
5. **资源管理模块**：监控GPU资源使用

### Gradio组件使用

- 使用`gr.Blocks()`创建自定义布局
- 使用`gr.Tab()`实现多标签页界面
- 使用`gr.Plot()`展示实时训练曲线
- 使用`gr.DataFrame()`展示数据与结果
- 使用`gr.State()`管理训练状态

### 训练过程控制

- 使用Python多进程管理训练任务
- 使用队列实现训练进程与UI进程通信
- 实现训练中断与恢复机制
- 提供训练日志实时展示

## 部署要求

- Python 3.9+
- CUDA 11.8+
- 至少16GB GPU显存
- 足够的磁盘空间用于模型和数据存储