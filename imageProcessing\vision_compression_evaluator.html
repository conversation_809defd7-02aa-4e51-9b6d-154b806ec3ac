<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>图像压缩 - 视觉模型效果评估器</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 40px;
            max-width: 1400px;
            margin: 0 auto;
            width: 100%;
        }

        .header {
            text-align: center;
            margin-bottom: 40px;
        }

        .title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 20px;
        }

        .upload-section {
            border: 3px dashed #667eea;
            border-radius: 20px;
            padding: 40px;
            margin-bottom: 30px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            transition: all 0.3s ease;
            cursor: pointer;
            text-align: center;
        }

        .upload-section:hover {
            border-color: #f093fb;
            background: linear-gradient(145deg, #f0f4ff, #e0e8ff);
            transform: translateY(-3px);
        }

        .upload-section.dragover {
            border-color: #f093fb;
            background: linear-gradient(145deg, #e8edff, #d8e3ff);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 3rem;
            color: #667eea;
            margin-bottom: 15px;
        }

        .upload-text {
            font-size: 1.2rem;
            color: #333;
            margin-bottom: 8px;
            font-weight: 600;
        }

        .upload-hint {
            color: #666;
            font-size: 0.9rem;
        }

        #fileInput {
            display: none;
        }

        .settings-panel {
            display: grid;
            grid-template-columns: 1fr 1fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .setting-group {
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            padding: 20px;
            border-radius: 15px;
        }

        .setting-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: block;
            font-size: 1rem;
        }

        .api-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 0.95rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .api-input:focus {
            border-color: #667eea;
        }

        .instruction-input {
            width: 100%;
            padding: 10px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 0.95rem;
            outline: none;
            resize: vertical;
            min-height: 80px;
            transition: border-color 0.3s ease;
        }

        .instruction-input:focus {
            border-color: #667eea;
        }

        .compression-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
            gap: 8px;
            margin-bottom: 15px;
        }

        .compression-item {
            position: relative;
        }

        .compression-checkbox {
            display: none;
        }

        .compression-label {
            display: block;
            padding: 8px 6px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-size: 0.8rem;
            font-weight: 500;
            line-height: 1.2;
        }

        .compression-checkbox:checked + .compression-label {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
            transform: scale(1.05);
        }

        .current-image {
            text-align: center;
            margin-bottom: 30px;
            padding: 20px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            border-radius: 15px;
        }

        .current-image img {
            max-width: 300px;
            max-height: 300px;
            border-radius: 10px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
        }

        .image-info {
            margin-top: 15px;
            color: #666;
            font-size: 0.9rem;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-primary:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-secondary {
            background: linear-gradient(135deg, #a0a0a0, #808080);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(128, 128, 128, 0.3);
        }

        .btn-secondary:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-original {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-original:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-original:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-compressed {
            background: linear-gradient(135deg, #ff6b6b, #ee5a52);
            color: white;
        }

        .btn-compressed:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(255, 107, 107, 0.4);
        }

        .btn-compressed:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .btn-similarity {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-similarity:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn-similarity:disabled {
            background: #ccc;
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-bottom: 30px;
        }

        .results-section {
            margin-top: 30px;
        }

        .results-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .results-title {
            font-size: 1.5rem;
            font-weight: 600;
            color: #333;
        }

        .progress-info {
            color: #666;
            font-size: 0.9rem;
        }

        .results-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
            gap: 20px;
        }

        .result-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            transition: transform 0.2s ease;
            position: relative;
        }

        .result-item:hover {
            transform: translateY(-3px);
        }

        .result-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .result-title {
            font-weight: 600;
            color: #333;
            font-size: 1.1rem;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.8rem;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-processing {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .result-image {
            text-align: center;
            margin-bottom: 15px;
        }

        .result-image img {
            max-width: 150px;
            max-height: 150px;
            border-radius: 8px;
            border: 2px solid #eee;
        }

        .result-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-bottom: 15px;
            font-size: 0.85rem;
        }

        .stat-item {
            background: #f8f9fa;
            padding: 8px 10px;
            border-radius: 6px;
            text-align: center;
        }

        .stat-label {
            color: #666;
            font-size: 0.75rem;
            margin-bottom: 2px;
        }

        .stat-value {
            font-weight: 600;
            color: #333;
        }

        .result-response {
            background: #f8f9fa;
            padding: 12px;
            border-radius: 8px;
            border-left: 4px solid #667eea;
            font-size: 0.9rem;
            line-height: 1.4;
            color: #555;
            max-height: 120px;
            overflow-y: auto;
        }

        .similarity-score {
            text-align: center;
            margin-top: 10px;
            padding: 10px;
            border-radius: 8px;
            font-weight: 600;
        }

        .similarity-excellent {
            background: #d4edda;
            color: #155724;
        }

        .similarity-good {
            background: #d1ecf1;
            color: #0c5460;
        }

        .similarity-fair {
            background: #fff3cd;
            color: #856404;
        }

        .similarity-poor {
            background: #f8d7da;
            color: #721c24;
        }

        .summary-panel {
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            border-radius: 20px;
            padding: 25px;
            margin-top: 30px;
        }

        .summary-title {
            font-size: 1.3rem;
            font-weight: 600;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }

        .summary-stats {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
            margin-bottom: 20px;
        }

        .summary-stat {
            text-align: center;
            background: white;
            padding: 15px;
            border-radius: 10px;
        }

        .summary-number {
            font-size: 1.8rem;
            font-weight: 700;
            color: #667eea;
            display: block;
            margin-bottom: 5px;
        }

        .summary-label {
            color: #666;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .hidden {
            display: none;
        }

        .loading {
            position: relative;
        }

        .loading::after {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .loading::before {
            content: '⏳';
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            font-size: 2rem;
            z-index: 1;
            animation: spin 2s linear infinite;
        }

        @keyframes spin {
            from { transform: translate(-50%, -50%) rotate(0deg); }
            to { transform: translate(-50%, -50%) rotate(360deg); }
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .settings-panel {
                grid-template-columns: 1fr;
            }
            
            .setting-group[style*="grid-column"] {
                grid-column: 1 / -1 !important;
            }
            
            .results-grid {
                grid-template-columns: 1fr;
            }
            
            .compression-grid {
                grid-template-columns: repeat(2, 1fr);
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">图像压缩 - 视觉模型效果评估器</h1>
            <p class="subtitle">测试不同压缩设置对视觉识别结果的影响，找到最佳的质量与性能平衡点</p>
        </div>

        <div class="upload-section" id="uploadArea">
            <div class="upload-icon">🖼️</div>
            <div class="upload-text">选择或拖拽图片到此处</div>
            <div class="upload-hint">支持 PNG、JPG、JPEG、BMP、TIFF、WebP 等格式</div>
        </div>
        
        <input type="file" id="fileInput" accept="image/*">

        <div class="current-image hidden" id="currentImageSection">
            <img id="currentImage" alt="当前图片">
            <div class="image-info" id="imageInfo"></div>
        </div>

        <div class="settings-panel">
            <div class="setting-group" style="grid-column: 1 / -1;">
                <label class="setting-label">压缩设置</label>
                <div class="compression-grid">
                    <div class="compression-item">
                        <input type="checkbox" id="original" class="compression-checkbox" checked>
                        <label for="original" class="compression-label">原图<br><small>基准</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg100" class="compression-checkbox">
                        <label for="jpg100" class="compression-label">JPG<br><small>100%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg90" class="compression-checkbox">
                        <label for="jpg90" class="compression-label">JPG<br><small>90%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg80" class="compression-checkbox">
                        <label for="jpg80" class="compression-label">JPG<br><small>80%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg70" class="compression-checkbox" checked>
                        <label for="jpg70" class="compression-label">JPG<br><small>70%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg60" class="compression-checkbox">
                        <label for="jpg60" class="compression-label">JPG<br><small>60%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg50" class="compression-checkbox">
                        <label for="jpg50" class="compression-label">JPG<br><small>50%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg40" class="compression-checkbox">
                        <label for="jpg40" class="compression-label">JPG<br><small>40%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="jpg30" class="compression-checkbox">
                        <label for="jpg30" class="compression-label">JPG<br><small>30%</small></label>
                    </div>
                    <div class="compression-item">
                        <input type="checkbox" id="webp" class="compression-checkbox" checked>
                        <label for="webp" class="compression-label">WebP<br><small>70%</small></label>
                    </div>
                </div>
            </div>
        </div>

        <div class="settings-panel" id="evaluationSettings" style="display: none;">
            <div class="setting-group">
                <label class="setting-label">API 地址</label>
                <input type="text" class="api-input" id="apiUrl" value="http://27.159.93.61:8198" placeholder="输入视觉模型 API 地址">
            </div>
            
            <div class="setting-group" style="grid-column: 2 / -1;">
                <label class="setting-label">测试指令</label>
                <textarea class="instruction-input" id="instruction" placeholder="请描述这张图片的内容">请详细描述一下这张图片.</textarea>
            </div>

            <div class="setting-group" style="grid-column: 1 / -1;">
                <label class="setting-label">模型设置</label>
                <div style="display: flex; align-items: center; gap: 10px; margin-top: 10px;">
                    <input type="checkbox" id="deterministicMode" style="margin: 0;">
                    <label for="deterministicMode" style="margin: 0; font-weight: normal; color: #666;">
                        🎯 确定性模式 (固定种子，去除随机性)
                    </label>
                </div>
                <div style="font-size: 0.8rem; color: #888; margin-top: 5px;">
                    启用后，相同输入将产生相同输出，适用于可重现的测试
                </div>
            </div>
        </div>

        <div class="action-buttons">
            <button class="btn btn-primary" id="compressBtn" onclick="startCompression()">
                🗜️ 开始压缩
            </button>
        </div>

        <div class="results-section hidden" id="resultsSection">
            <div class="results-header">
                <h3 class="results-title">测试结果</h3>
                <div class="progress-info" id="progressInfo">准备中...</div>
            </div>
            <div class="results-grid" id="resultsGrid"></div>
        </div>

        <div class="summary-panel hidden" id="summaryPanel">
            <div class="summary-title">📊 测试总结</div>
            <div class="summary-stats" id="summaryStats"></div>
            <div id="recommendations"></div>
        </div>
    </div>

    <script>
        let currentFile = null;
        let originalResponse = '';
        let testResults = [];
        let compressedResults = [];
        let isCompressing = false;

        // 压缩配置
        const compressionConfigs = {
            'original': { format: 'original', quality: 100, displayName: '原图 (基准)' },
            'jpg100': { format: 'jpeg', quality: 100, displayName: 'JPG (100%)' },
            'jpg90': { format: 'jpeg', quality: 90, displayName: 'JPG (90%)' },
            'jpg80': { format: 'jpeg', quality: 80, displayName: 'JPG (80%)' },
            'jpg70': { format: 'jpeg', quality: 70, displayName: 'JPG (70%)' },
            'jpg60': { format: 'jpeg', quality: 60, displayName: 'JPG (60%)' },
            'jpg50': { format: 'jpeg', quality: 50, displayName: 'JPG (50%)' },
            'jpg40': { format: 'jpeg', quality: 40, displayName: 'JPG (40%)' },
            'jpg30': { format: 'jpeg', quality: 30, displayName: 'JPG (30%)' },
            'webp': { format: 'webp', quality: 70, displayName: 'WebP (70%)' }
        };

        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');

            // 上传区域点击
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);
        });

        function handleFileSelect(event) {
            const file = event.target.files[0];
            if (file && file.type.startsWith('image/')) {
                loadImage(file);
            } else {
                // 显示友好的提示信息
                const uploadArea = document.getElementById('uploadArea');
                uploadArea.innerHTML = `
                    <div class="upload-icon">❌</div>
                    <div class="upload-text" style="color: #d63384;">文件类型不支持</div>
                    <div class="upload-hint" style="color: #d63384;">请选择图像文件（PNG、JPG、JPEG、BMP、TIFF、WebP）</div>
                `;
                setTimeout(() => {
                    uploadArea.innerHTML = `
                        <div class="upload-icon">🖼️</div>
                        <div class="upload-text">选择或拖拽图片到此处</div>
                        <div class="upload-hint">支持 PNG、JPG、JPEG、BMP、TIFF、WebP 等格式</div>
                    `;
                }, 3000);
            }
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            const uploadArea = document.getElementById('uploadArea');
            uploadArea.classList.remove('dragover');
            
            const files = Array.from(event.dataTransfer.files);
            const imageFile = files.find(file => file.type.startsWith('image/'));
            
            if (imageFile) {
                loadImage(imageFile);
            } else {
                // 显示友好的提示信息
                uploadArea.innerHTML = `
                    <div class="upload-icon">❌</div>
                    <div class="upload-text" style="color: #d63384;">文件类型不支持</div>
                    <div class="upload-hint" style="color: #d63384;">请拖拽图像文件到此处（PNG、JPG、JPEG、BMP、TIFF、WebP）</div>
                `;
                setTimeout(() => {
                    uploadArea.innerHTML = `
                        <div class="upload-icon">🖼️</div>
                        <div class="upload-text">选择或拖拽图片到此处</div>
                        <div class="upload-hint">支持 PNG、JPG、JPEG、BMP、TIFF、WebP 等格式</div>
                    `;
                }, 3000);
            }
        }

        function loadImage(file) {
            currentFile = file;
            const reader = new FileReader();
            
            reader.onload = function(e) {
                const img = document.getElementById('currentImage');
                img.src = e.target.result;
                
                document.getElementById('currentImageSection').classList.remove('hidden');
                document.getElementById('imageInfo').innerHTML = `
                    <strong>文件名:</strong> ${file.name}<br>
                    <strong>大小:</strong> ${formatFileSize(file.size)}<br>
                    <strong>类型:</strong> ${file.type}
                `;
                
                // 重置结果
                resetResults();
            };
            
            reader.readAsDataURL(file);
        }

        function resetResults() {
            testResults = [];
            compressedResults = [];
            originalResponse = '';
            document.getElementById('resultsSection').classList.add('hidden');
            document.getElementById('summaryPanel').classList.add('hidden');
            document.getElementById('evaluationSettings').style.display = 'none';
            document.getElementById('resultsGrid').innerHTML = '';
            document.getElementById('compressBtn').classList.remove('hidden');
            document.getElementById('compressBtn').disabled = false;
            document.getElementById('compressBtn').textContent = '🗜️ 开始压缩';
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        function getCompressedFormats() {
            const formats = compressedResults.map(r => r.config.displayName);
            return formats.join('、');
        }

        function showCompressionSummary() {
            // 计算压缩统计
            const totalOriginalSize = currentFile.size;
            const avgCompressionRatio = compressedResults
                .filter(r => r.config.format !== 'original')
                .reduce((sum, r) => sum + r.compressionRatio, 0) / (compressedResults.length - 1);
            
            const bestCompression = compressedResults
                .filter(r => r.config.format !== 'original')
                .reduce((best, current) => 
                    current.compressionRatio > best.compressionRatio ? current : best, 
                    compressedResults.find(r => r.config.format !== 'original') || { compressionRatio: 0 }
                );

            // 更新进度信息为摘要
            document.getElementById('progressInfo').innerHTML = `
                <div style="text-align: left; line-height: 1.5;">
                    <strong>📊 压缩摘要</strong><br>
                    🔢 总计：${compressedResults.length} 个版本<br>
                    📉 平均压缩：${avgCompressionRatio.toFixed(1)}%<br>
                    🏆 最高压缩：${bestCompression.config.displayName} (${bestCompression.compressionRatio.toFixed(1)}%)<br>
                    ⏭️ 下一步：点击卡片上的 "识别" 按钮进行评估
                </div>
            `;
        }

        async function startCompression() {
            if (!currentFile) {
                // 显示友好的提示信息
                document.getElementById('resultsSection').classList.remove('hidden');
                document.getElementById('progressInfo').innerHTML = `
                    <div style="color: #721c24; background: #f8d7da; padding: 15px; border-radius: 8px; border: 1px solid #f5c6cb; margin-bottom: 20px;">
                        📷 <strong>请先选择一张图片！</strong><br>
                        💡 点击上方区域选择图片，或直接拖拽图片到上传区域
                    </div>
                `;
                return;
            }

            // 获取选中的压缩配置
            const selectedConfigs = [];
            Object.keys(compressionConfigs).forEach(key => {
                if (document.getElementById(key).checked) {
                    selectedConfigs.push({
                        id: key,
                        ...compressionConfigs[key]
                    });
                }
            });

            if (selectedConfigs.length === 0) {
                // 显示友好的提示信息，而不是弹窗
                document.getElementById('resultsSection').classList.remove('hidden');
                document.getElementById('progressInfo').innerHTML = `
                    <div style="color: #856404; background: #fff3cd; padding: 15px; border-radius: 8px; border: 1px solid #ffeaa7; margin-bottom: 20px;">
                        ⚠️ <strong>请至少选择一种压缩设置！</strong><br>
                        💡 建议选择：原图（作为基准）、WebP、JPG
                    </div>
                `;
                return;
            }

            isCompressing = true;
            compressedResults = [];

            document.getElementById('compressBtn').disabled = true;
            document.getElementById('compressBtn').textContent = '🔄 压缩中...';
            document.getElementById('resultsSection').classList.remove('hidden');
            document.getElementById('progressInfo').textContent = `0 / ${selectedConfigs.length} 已压缩`;

            // 创建结果显示
            createResultItems(selectedConfigs);

            // 压缩所有图片
            for (let i = 0; i < selectedConfigs.length; i++) {
                const config = selectedConfigs[i];
                await processCompression(config);
                updateProgress(i + 1, selectedConfigs.length, '已压缩');
            }

            isCompressing = false;
            document.getElementById('compressBtn').disabled = false;
            document.getElementById('compressBtn').textContent = '🗜️ 重新压缩';

            // 显示评估设置和按钮
            document.getElementById('evaluationSettings').style.display = 'grid';
            
            // 显示压缩结果摘要
            showCompressionSummary();
        }

        async function startSingleEvaluation(configId) {
            const resultToEvaluate = compressedResults.find(r => r.config.id === configId);
            if (!resultToEvaluate) {
                document.getElementById(`response-${configId}`).textContent = '❌ 找不到要评估的结果！';
                return;
            }

            const apiUrl = document.getElementById('apiUrl').value.trim();
            const instruction = document.getElementById('instruction').value.trim();

            if (!apiUrl) {
                document.getElementById(`response-${configId}`).textContent = '⚠️ 请先输入视觉模型 API 地址！';
                document.getElementById('apiUrl').focus();
                return;
            }
            if (!instruction) {
                document.getElementById(`response-${configId}`).textContent = '⚠️ 请先输入测试指令！';
                document.getElementById('instruction').focus();
                return;
            }

            // 禁用当前按钮（防止重复点击）
            const currentBtn = document.getElementById(`btn-recognize-${configId}`);
            if (currentBtn) {
                currentBtn.disabled = true;
            }

            await processEvaluation(resultToEvaluate, apiUrl, instruction);
            
            // 评估完成后重新启用当前按钮
            if (currentBtn) {
                currentBtn.disabled = false;
            }
            
            // 更新所有相似度按钮的状态
            enableSimilarityButtons();
        }

        function updateProgress(completed, total, status) {
            document.getElementById('progressInfo').textContent = `${completed} / ${total} ${status}`;
        }

        function createResultItems(configs) {
            const resultsGrid = document.getElementById('resultsGrid');
            resultsGrid.innerHTML = '';

            configs.forEach(config => {
                const resultItem = document.createElement('div');
                resultItem.className = 'result-item';
                resultItem.id = `result-${config.id}`;

                resultItem.innerHTML = `
                    <div class="result-header">
                        <div class="result-title">${config.displayName}</div>
                        <div class="status-badge status-pending" id="status-${config.id}">等待压缩</div>
                    </div>
                    <div class="result-image">
                        <img id="img-${config.id}" alt="压缩图片" style="display: none;">
                    </div>
                    <div class="result-stats">
                        <div class="stat-item">
                            <div class="stat-label">文件大小</div>
                            <div class="stat-value" id="size-${config.id}">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">压缩比</div>
                            <div class="stat-value" id="ratio-${config.id}">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">响应时间</div>
                            <div class="stat-value" id="time-${config.id}">-</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-label">相似度评分</div>
                            <div class="stat-value" id="similarity-${config.id}">-</div>
                        </div>
                    </div>
                    <div class="result-response" id="response-${config.id}">等待操作...</div>
                    <div class="similarity-score hidden" id="score-${config.id}"></div>
                    <div style="display: flex; gap: 10px; justify-content: right; margin-top: 15px;">
                        <button class="btn ${config.format === 'original' ? 'btn-original' : 'btn-compressed'} recognize-btn" id="btn-recognize-${config.id}" onclick="startSingleEvaluation('${config.id}')" disabled>
                            🔍 识别
                        </button>
                        ${config.format !== 'original' ? `
                        <button class="btn btn-similarity" id="btn-similarity-${config.id}" onclick="startSimilarityEvaluation('${config.id}')" disabled>
                            📊 相似度
                        </button>
                        ` : ''}
                    </div>
                `;

                resultsGrid.appendChild(resultItem);
            });
        }

        function updateResultItemsForEvaluation() {
            compressedResults.forEach(result => {
                const config = result.config;
                document.getElementById(`status-${config.id}`).textContent = '等待评估';
                document.getElementById(`status-${config.id}`).className = 'status-badge status-pending';
                document.getElementById(`response-${config.id}`).textContent = '等待视觉评估...';
                document.getElementById(`time-${config.id}`).textContent = '-';
                document.getElementById(`similarity-${config.id}`).textContent = '-';
                document.getElementById(`score-${config.id}`).classList.add('hidden');
            });
        }

        async function processCompression(config) {
            const resultId = config.id;
            
            try {
                // 更新状态
                document.getElementById(`status-${resultId}`).textContent = '压缩中';
                document.getElementById(`status-${resultId}`).className = 'status-badge status-processing';
                document.getElementById(`result-${resultId}`).classList.add('loading');

                // 压缩图片
                const compressedData = await compressImage(currentFile, config);
                
                // 显示压缩后的图片
                const img = document.getElementById(`img-${resultId}`);
                img.src = compressedData.dataUrl;
                img.style.display = 'block';

                // 更新文件大小和压缩比
                document.getElementById(`size-${resultId}`).textContent = formatFileSize(compressedData.size);
                const compressionRatio = ((1 - compressedData.size / currentFile.size) * 100).toFixed(1);
                document.getElementById(`ratio-${resultId}`).textContent = compressionRatio + '%';

                // 保存压缩结果
                compressedResults.push({
                    config,
                    dataUrl: compressedData.dataUrl,
                    size: compressedData.size,
                    compressionRatio: parseFloat(compressionRatio)
                });

                // 更新状态为完成
                document.getElementById(`status-${resultId}`).textContent = '压缩完成';
                document.getElementById(`status-${resultId}`).className = 'status-badge status-completed';
                document.getElementById(`response-${resultId}`).textContent = '压缩完成，点击"识别"按钮进行评估。';

                // 启用识别按钮
                const recognizeBtn = document.getElementById(`btn-recognize-${resultId}`);
                if(recognizeBtn) {
                     // 启用所有压缩完成的按钮
                    recognizeBtn.disabled = false;
                }

                // 相似度按钮的状态由enableSimilarityButtons统一管理

            } catch (error) {
                console.error(`压缩 ${config.displayName} 时出错:`, error);
                
                // 更新错误状态
                document.getElementById(`status-${resultId}`).textContent = '压缩失败';
                document.getElementById(`status-${resultId}`).className = 'status-badge status-error';
                document.getElementById(`response-${resultId}`).textContent = `压缩失败: ${error.message}`;
            } finally {
                document.getElementById(`result-${resultId}`).classList.remove('loading');
            }
        }

        async function processEvaluation(compressedResult, apiUrl, instruction) {
            const config = compressedResult.config;
            const resultId = config.id;
            const recognizeBtn = document.getElementById(`btn-recognize-${resultId}`);
            
            try {
                // 更新状态
                if (recognizeBtn) {
                    recognizeBtn.textContent = '🔄 识别中...';
                }
                document.getElementById(`status-${resultId}`).textContent = '评估中';
                document.getElementById(`status-${resultId}`).className = 'status-badge status-processing';
                document.getElementById(`result-${resultId}`).classList.add('loading');

                const startTime = Date.now();

                // 发送请求到视觉模型
                const response = await sendToVisionModel(apiUrl, instruction, compressedResult.dataUrl);
                const responseTime = Date.now() - startTime;

                // 更新响应时间
                document.getElementById(`time-${resultId}`).textContent = responseTime + 'ms';
                document.getElementById(`response-${resultId}`).textContent = response;

                // 如果是原图，保存作为基准
                if (config.format === 'original') {
                    originalResponse = response;
                }
                
                // 每次识别完成后，更新相似度按钮状态
                enableSimilarityButtons();

                // 保存结果
                testResults.push({
                    config,
                    size: compressedResult.size,
                    compressionRatio: compressedResult.compressionRatio,
                    responseTime,
                    response
                });

                // 更新状态为完成
                document.getElementById(`status-${resultId}`).textContent = '评估完成';
                document.getElementById(`status-${resultId}`).className = 'status-badge status-completed';

                if (recognizeBtn) {
                    recognizeBtn.textContent = '✅ 识别完成';
                }

            } catch (error) {
                console.error(`评估 ${config.displayName} 时出错:`, error);
                
                // 更新错误状态
                if (recognizeBtn) {
                    recognizeBtn.textContent = '❌ 识别失败';
                }
                document.getElementById(`status-${resultId}`).textContent = '评估失败';
                document.getElementById(`status-${resultId}`).className = 'status-badge status-error';
                
                // 【改进错误显示】提供更详细的错误信息和解决建议
                let errorMessage = `评估失败: ${error.message}`;
                if (error.message.includes('400')) {
                    errorMessage += '\n💡 可能是图片格式问题，请检查API是否支持当前图片格式';
                } else if (error.message.includes('500')) {
                    errorMessage += '\n💡 服务器内部错误，建议降低并发数或稍后重试';
                } else if (error.message.includes('timeout') || error.message.includes('网络')) {
                    errorMessage += '\n💡 网络超时，建议检查网络连接或降低并发数';
                } else if (error.message.includes('decode') || error.message.includes('image')) {
                    errorMessage += '\n💡 图片解析失败，可能是Base64格式问题';
                }
                
                document.getElementById(`response-${resultId}`).textContent = errorMessage;
                
                // 记录错误结果
                testResults.push({
                    config,
                    error: error.message,
                    size: compressedResult.size,
                    compressionRatio: compressedResult.compressionRatio,
                    responseTime: 0,
                    response: ''
                });
            } finally {
                document.getElementById(`result-${resultId}`).classList.remove('loading');
            }
        }

        async function compressImage(file, config) {
            if (config.format === 'original') {
                // 原图直接返回
                const reader = new FileReader();
                return new Promise((resolve) => {
                    reader.onload = function(e) {
                        resolve({
                            dataUrl: e.target.result,
                            size: file.size
                        });
                    };
                    reader.readAsDataURL(file);
                });
            }

            return new Promise((resolve, reject) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    canvas.width = img.width;
                    canvas.height = img.height;

                    // 如果是 JPEG，填充白色背景
                    if (config.format === 'jpeg') {
                        ctx.fillStyle = 'white';
                        ctx.fillRect(0, 0, canvas.width, canvas.height);
                    }

                    ctx.drawImage(img, 0, 0);

                    const mimeType = config.format === 'jpeg' ? 'image/jpeg' : `image/${config.format}`;
                    const quality = config.quality / 100;

                    canvas.toBlob(function(blob) {
                        if (blob) {
                            const reader = new FileReader();
                            reader.onload = function(e) {
                                resolve({
                                    dataUrl: e.target.result,
                                    size: blob.size
                                });
                            };
                            reader.readAsDataURL(blob);
                        } else {
                            reject(new Error('图片压缩失败'));
                        }
                    }, mimeType, quality);
                };

                img.onerror = function() {
                    reject(new Error('图片加载失败'));
                };

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

        async function sendToVisionModel(apiUrl, instruction, imageDataUrl) {
            // 【关键修复】直接使用原始的 Data URL，它包含了正确的 MIME 类型
            // 例如 "data:image/webp;base64,..." 或 "data:image/jpeg;base64,..."
            // 这样服务器就能知道如何正确解码图片。

            // 检查是否启用确定性模式
            const isDeterministic = document.getElementById('deterministicMode').checked;
            
            const requestBody = {
                max_tokens: 200,
                messages: [
                    {
                        role: 'user',
                        content: [
                            { type: 'text', text: instruction },
                            { 
                                type: 'image_url', 
                                image_url: { url: imageDataUrl } // 直接使用原始 Data URL
                            }
                        ]
                    }
                ],
                stream: false,
                stop: null
            };

            // 根据确定性模式设置参数
            if (isDeterministic) {
                requestBody.temperature = 0;
                requestBody.top_p = 1.0;
                requestBody.seed = 12345;
                requestBody.frequency_penalty = 0;
                requestBody.presence_penalty = 0;
            } else {
                requestBody.temperature = 0.1;
            }

            const response = await fetch(`${apiUrl}/v1/chat/completions`, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(requestBody)
            });

            if (!response.ok) {
                const errorText = await response.text();
                console.error(`API 错误详情:`, {
                    status: response.status,
                    statusText: response.statusText,
                    errorText: errorText
                });
                throw new Error(`API 请求失败: ${response.status} - ${errorText}`);
            }

            const data = await response.json();
            
            if (!data.choices || !data.choices[0] || !data.choices[0].message) {
                throw new Error('API 响应格式不正确');
            }
            
            return data.choices[0].message.content;
        }

        async function calculateSimilarity(text1, text2) {
            // 使用模型进行语义相似度计算
            if (!text1 || !text2) {
                return 0;
            }
            
            // 如果两个文本完全相同，直接返回100%
            if (text1.trim() === text2.trim()) {
                return 100;
            }
            
            try {
                const apiUrl = document.getElementById('apiUrl').value;
                if (!apiUrl) {
                    console.warn('未设置API地址，回退到简单相似度计算');
                    return calculateSimpleSimilarity(text1, text2);
                }

                // 检查是否启用确定性模式
                const isDeterministic = document.getElementById('deterministicMode').checked;
                
                const requestBody = {
                    max_tokens: 50,
                    messages: [
                        {
                            role: 'user',
                            content: `请分析以下两段图像描述的语义相似度，并给出0-100的分数（只返回数字）：

文本1：${text1}

文本2：${text2}

评分标准：
- 100分：语义完全相同
- 90-99分：语义高度相似，细节略有差异
- 80-89分：语义相似，但有明显差异
- 70-79分：语义部分相似
- 60-69分：语义略有相似
- 0-59分：语义差异很大或完全不同

请只返回分数数字，不要其他内容。`
                        }
                    ],
                    stream: false,
                    stop: null
                };

                // 根据确定性模式设置参数
                if (isDeterministic) {
                    requestBody.temperature = 0;
                    requestBody.top_p = 1.0;
                    requestBody.seed = 54321; // 与识别任务使用不同的种子
                    requestBody.frequency_penalty = 0;
                    requestBody.presence_penalty = 0;
                } else {
                    requestBody.temperature = 0.1;
                }

                const response = await fetch(`${apiUrl}/v1/chat/completions`, {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json'
                    },
                    body: JSON.stringify(requestBody)
                });

                if (!response.ok) {
                    console.warn('语义相似度API调用失败，回退到简单计算');
                    return calculateSimpleSimilarity(text1, text2);
                }

                const data = await response.json();
                const content = data.choices?.[0]?.message?.content?.trim();
                
                if (content) {
                    // 从返回内容中提取数字
                    const match = content.match(/\d+(\.\d+)?/);
                    if (match) {
                        const score = parseFloat(match[0]);
                        return Math.min(100, Math.max(0, score)); // 确保分数在0-100范围内
                    }
                }
                
                console.warn('无法解析相似度分数，回退到简单计算');
                return calculateSimpleSimilarity(text1, text2);
                
            } catch (error) {
                console.warn('语义相似度计算失败，回退到简单计算:', error.message);
                return calculateSimpleSimilarity(text1, text2);
            }
        }

        function calculateSimpleSimilarity(text1, text2) {
            // 简单的相似度计算，基于词汇重叠（作为备用方案）
            const words1 = text1.toLowerCase().split(/\W+/).filter(word => word.length > 0);
            const words2 = text2.toLowerCase().split(/\W+/).filter(word => word.length > 0);
            
            const set1 = new Set(words1);
            const set2 = new Set(words2);
            
            const intersection = new Set([...set1].filter(x => set2.has(x)));
            const union = new Set([...set1, ...set2]);
            
            return union.size > 0 ? (intersection.size / union.size) * 100 : 0;
        }

        function enableSimilarityButtons() {
            // 当原图识别完成后，检查每个压缩图是否已识别，如果已识别则启用相似度按钮
            if (!originalResponse) return; // 原图未识别，不启用任何相似度按钮
            
            compressedResults.forEach(result => {
                if (result.config.format !== 'original') {
                    const similarityBtn = document.getElementById(`btn-similarity-${result.config.id}`);
                    if (similarityBtn) {
                        // 检查该图片是否已经识别完成
                        const targetResult = testResults.find(r => r.config.id === result.config.id);
                        const hasRecognitionResult = targetResult && targetResult.response && !targetResult.error;
                        
                        similarityBtn.disabled = !hasRecognitionResult;
                    }
                }
            });
        }

        async function startSimilarityEvaluation(configId) {
            if (!originalResponse) {
                const responseEl = document.getElementById(`response-${configId}`);
                responseEl.textContent = '⚠️ 请先完成原图识别！';
                responseEl.style.background = '#fff3cd';
                responseEl.style.borderLeft = '4px solid #ffc107';
                return;
            }

            // 找到对应的识别结果
            const targetResult = testResults.find(r => r.config.id === configId);
            if (!targetResult || !targetResult.response || targetResult.error) {
                const responseEl = document.getElementById(`response-${configId}`);
                responseEl.textContent = '⚠️ 请先完成该图片的识别！';
                responseEl.style.background = '#fff3cd';
                responseEl.style.borderLeft = '4px solid #ffc107';
                return;
            }

            const similarityBtn = document.getElementById(`btn-similarity-${configId}`);
            
            try {
                // 更新按钮状态
                if (similarityBtn) {
                    similarityBtn.textContent = '🔄 计算中...';
                    similarityBtn.disabled = true;
                }

                // 显示计算中状态
                document.getElementById(`similarity-${configId}`).textContent = '计算中...';

                // 计算相似度
                const similarity = await calculateSimilarity(originalResponse, targetResult.response);

                // 更新相似度显示
                document.getElementById(`similarity-${configId}`).textContent = similarity.toFixed(1) + '%';
                
                const scoreElement = document.getElementById(`score-${configId}`);
                scoreElement.classList.remove('hidden');
                
                if (similarity >= 90) {
                    scoreElement.className = 'similarity-score similarity-excellent';
                    scoreElement.textContent = `✅ 极佳 (${similarity.toFixed(1)}%)`;
                } else if (similarity >= 80) {
                    scoreElement.className = 'similarity-score similarity-good';
                    scoreElement.textContent = `✨ 良好 (${similarity.toFixed(1)}%)`;
                } else if (similarity >= 70) {
                    scoreElement.className = 'similarity-score similarity-fair';
                    scoreElement.textContent = `⚠️ 一般 (${similarity.toFixed(1)}%)`;
                } else {
                    scoreElement.className = 'similarity-score similarity-poor';
                    scoreElement.textContent = `❌ 较差 (${similarity.toFixed(1)}%)`;
                }

                // 保存相似度结果到对应的测试结果中
                targetResult.similarity = similarity;

                // 更新按钮状态
                if (similarityBtn) {
                    similarityBtn.textContent = '✅ 已评分';
                }

            } catch (error) {
                console.error(`相似度评估 ${configId} 时出错:`, error);
                
                document.getElementById(`similarity-${configId}`).textContent = '评估失败';
                
                if (similarityBtn) {
                    similarityBtn.textContent = '❌ 评估失败';
                }
            } finally {
                if (similarityBtn) {
                    similarityBtn.disabled = false;
                }
            }
        }

        function generateSummary() {
            document.getElementById('summaryPanel').classList.remove('hidden');
            
            const successResults = testResults.filter(r => !r.error);
            const totalSize = currentFile.size;
            const bestCompression = successResults.reduce((best, current) => 
                current.compressionRatio > best.compressionRatio ? current : best, 
                successResults[0] || { compressionRatio: 0 }
            );
            const bestSimilarity = successResults.reduce((best, current) => 
                (current.similarity || 0) > (best.similarity || 0) ? current : best, 
                successResults[0] || { similarity: 0 }
            );

            const avgResponseTime = successResults.length > 0 ? 
                (successResults.reduce((sum, r) => sum + r.responseTime, 0) / successResults.length).toFixed(0) : 0;

            const summaryStats = document.getElementById('summaryStats');
            summaryStats.innerHTML = `
                <div class="summary-stat">
                    <span class="summary-number">${successResults.length}</span>
                    <div class="summary-label">成功测试</div>
                </div>
                <div class="summary-stat">
                    <span class="summary-number">${bestCompression.compressionRatio?.toFixed(1) || 0}%</span>
                    <div class="summary-label">最高压缩比</div>
                </div>
                <div class="summary-stat">
                    <span class="summary-number">${bestSimilarity.similarity?.toFixed(1) || 0}%</span>
                    <div class="summary-label">最高相似度</div>
                </div>
                <div class="summary-stat">
                    <span class="summary-number">${avgResponseTime}ms</span>
                    <div class="summary-label">平均响应时间</div>
                </div>
            `;

            // 生成推荐
            let recommendations = '<h4 style="margin-bottom: 15px; color: #333;">📋 推荐建议:</h4>';
            
            const goodResults = successResults.filter(r => (r.similarity || 0) >= 90 && r.compressionRatio > 0);
            if (goodResults.length > 0) {
                const recommended = goodResults.reduce((best, current) => 
                    current.compressionRatio > best.compressionRatio ? current : best
                );
                recommendations += `<p style="margin-bottom: 10px;">✅ <strong>推荐设置:</strong> ${recommended.config.displayName} - 在保持高相似度 (${(recommended.similarity || 0).toFixed(1)}%) 的同时实现了 ${recommended.compressionRatio.toFixed(1)}% 的压缩比。</p>`;
            }

            const poorResults = successResults.filter(r => (r.similarity || 0) < 70 && r.similarity !== undefined);
            if (poorResults.length > 0) {
                recommendations += `<p style="margin-bottom: 10px;">⚠️ <strong>避免使用:</strong> ${poorResults.map(r => r.config.displayName).join(', ')} - 这些设置导致了明显的质量损失。</p>`;
            }

            document.getElementById('recommendations').innerHTML = recommendations;
        }
    </script>
</body>
</html> 