# DeepSeek训练系统UI使用指南

## 系统要求

- Python 3.9+
- CUDA 11.8+
- 至少16GB GPU显存
- 足够的磁盘空间用于模型和数据存储

## 快速开始

### 在Windows上启动

```
start_ui.bat
```

或指定端口和其他参数：

```
start_ui.bat --port 8080 --listen 0.0.0.0 --share --gpu_id 0
```

### 在Linux/Mac上启动

```
./run.sh
```

或指定端口和其他参数：

```
./run.sh --port 8080 --listen 0.0.0.0 --share --gpu_id 0
```

## 命令行参数

- `--port <端口号>`: 指定UI服务的端口号，默认为7860
- `--listen <监听地址>`: 指定监听地址，默认为0.0.0.0（所有网络接口）
- `--share`: 创建公开链接，可通过互联网访问（通过Gradio提供）
- `--gpu_id <GPU ID>`: 指定使用的GPU ID，例如 0,1,2（多个GPU用逗号分隔）

## 使用流程

### 1. 模型选择与配置

1. 在"模型选择与配置"标签页中，选择要使用的基础模型
2. 根据需要选择训练模式（origin或distill）
3. 配置系统提示词
4. 设置LoRA参数（Rank、Alpha、目标模块）

### 2. 数据管理

1. 在"数据管理"标签页中，上传训练数据文件
   - 支持JSON、JSONL和Excel格式
   - 数据必须包含Question、Complex_CoT和Response字段
2. 点击"处理数据"按钮，处理并验证数据
3. 设置训练集比例
4. 在数据预览表格中查看数据样例

### 3. SFT训练

1. 在"SFT训练"标签页中，设置训练参数：
   - 批量大小
   - 学习率
   - 训练轮次
   - 梯度累积步数
   - 格式化函数
   - 混合精度
   - 输出目录
2. 点击"开始SFT训练"按钮启动训练
3. 在训练状态文本框中查看训练启动状态

### 4. RL训练

1. 在"RL训练"标签页中，设置训练参数：
   - 批量大小
   - 学习率
   - 最大步数
   - Beta值
   - GPU显存利用率（控制GPU资源占用峰值）
   - 奖励权重配置
   - 输出目录
2. 点击"开始RL训练"按钮启动训练
3. 在训练状态文本框中查看训练启动状态

### 5. 训练监控

1. 在"训练监控"标签页中，查看训练进度和指标：
   - 实时损失曲线
   - GPU使用情况
   - 学习率变化曲线
   - 当前GPU状态
   - 训练日志表格
2. 点击"刷新数据"按钮手动更新监控数据
3. 点击"停止训练"按钮中止训练
4. 点击"保存检查点"按钮手动保存当前模型

### 6. 模型评估

1. 在"模型评估"标签页中：
   - 输入模型路径（或留空使用LoRA加载方式）
   - 指定LoRA目录
   - 设置最大生成长度
   - 在文本框中输入测试问题
2. 点击"运行评估"按钮进行模型推理
3. 查看模型回答和评分指标

## 常见问题

### Q: 如何停止正在运行的训练？

A: 在"训练监控"标签页中点击"停止训练"按钮。

### Q: 如何保存训练中的模型？

A: 在"训练监控"标签页中点击"保存检查点"按钮，或等待训练完成自动保存。

### Q: 训练中断后如何恢复？

A: 目前不支持训练中断后的恢复，但您可以使用保存的检查点重新开始训练。

### Q: 如何使用已训练的模型进行推理？

A: 在"模型评估"标签页中，指定模型路径或LoRA目录，然后输入问题进行测试。

### Q: 如何指定使用特定的GPU？

A: 启动时使用`--gpu_id`参数，如`start_ui.bat --gpu_id 1`来使用ID为1的GPU。多个GPU可用逗号分隔，如`--gpu_id 0,1`。

### Q: 如何控制GPU显存使用量？

A: 在RL训练标签页中调整"GPU显存利用率"滑块，默认值为0.25（25%），增大此值可加速训练但可能导致显存不足。

## 高级配置

如需进行更复杂的配置，您可以直接修改以下文件：

- `app.py`: UI主程序
- `requirements.txt`: 依赖包配置
- `train_sft.py`: SFT训练脚本
- `train_rl.py`: RL训练脚本
- `eval.py`: 评估脚本 