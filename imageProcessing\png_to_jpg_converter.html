<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PNG to JPG 转换器</title>
    <!-- 引入JSZip库用于创建ZIP文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.1);
            padding: 40px;
            max-width: 800px;
            width: 100%;
            text-align: center;
        }

        .title {
            color: #333;
            font-size: 2.5rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #666;
            font-size: 1.1rem;
            margin-bottom: 40px;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 15px;
            padding: 60px 40px;
            margin-bottom: 30px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #764ba2;
            background: linear-gradient(145deg, #f0f4ff, #e0e8ff);
            transform: translateY(-2px);
        }

        .upload-area.dragover {
            border-color: #764ba2;
            background: linear-gradient(145deg, #e8edff, #d8e3ff);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4rem;
            color: #667eea;
            margin-bottom: 20px;
            display: block;
        }

        .upload-text {
            font-size: 1.3rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .upload-hint {
            color: #666;
            font-size: 1rem;
        }

        #fileInput {
            display: none;
        }

        .file-list {
            margin-top: 30px;
            text-align: left;
        }

        .file-item {
            background: white;
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.05);
            display: flex;
            align-items: center;
            gap: 15px;
            border-left: 4px solid #667eea;
        }

        .file-preview {
            width: 60px;
            height: 60px;
            border-radius: 8px;
            object-fit: cover;
            border: 2px solid #eee;
        }

        .file-info {
            flex: 1;
        }

        .file-name {
            font-weight: 600;
            color: #333;
            margin-bottom: 5px;
        }

        .file-size {
            color: #666;
            font-size: 0.9rem;
        }

        .progress-bar {
            width: 100%;
            height: 6px;
            background: #f0f0f0;
            border-radius: 3px;
            margin-top: 8px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2);
            border-radius: 3px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .file-status {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-text {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .status-pending {
            color: #f39c12;
        }

        .status-converting {
            color: #3498db;
        }

        .status-completed {
            color: #27ae60;
        }

        .status-error {
            color: #e74c3c;
        }

        .download-btn {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: transform 0.2s ease;
        }

        .download-btn:hover {
            transform: translateY(-1px);
        }

        .delete-btn {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            border: none;
            padding: 6px 8px;
            border-radius: 4px;
            cursor: pointer;
            font-size: 0.8rem;
            font-weight: 500;
            transition: transform 0.2s ease;
            margin-left: 8px;
        }

        .delete-btn:hover {
            transform: translateY(-1px);
        }

        .quality-control {
            margin: 20px 0;
            padding: 20px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            border-radius: 15px;
            text-align: left;
        }

        .quality-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .quality-slider {
            width: 100%;
            height: 6px;
            border-radius: 3px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .quality-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
        }

        .quality-slider::-moz-range-thumb {
            width: 20px;
            height: 20px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
            border: none;
        }

        .file-name-input {
            background: transparent;
            border: none;
            font-weight: 600;
            color: #333;
            font-size: inherit;
            outline: none;
            width: 100%;
            padding: 2px 4px;
            border-radius: 4px;
            transition: background-color 0.2s ease;
        }

        .file-name-input:focus {
            background-color: rgba(102, 126, 234, 0.1);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(231, 76, 60, 0.4);
        }

        .action-buttons {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .download-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 12px 30px;
            border: none;
            border-radius: 25px;
            font-size: 1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(102, 126, 234, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(149, 165, 166, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #27ae60, #2ecc71);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(39, 174, 96, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #3498db, #2980b9);
            color: white;
        }

        .btn-info:hover {
            transform: translateY(-2px);
            box-shadow: 0 8px 20px rgba(52, 152, 219, 0.4);
        }

        .stats {
            display: flex;
            justify-content: space-around;
            margin-top: 30px;
            padding: 20px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            border-radius: 15px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2rem;
            font-weight: 700;
            color: #667eea;
            display: block;
        }

        .stat-label {
            color: #666;
            font-size: 0.9rem;
            margin-top: 5px;
        }

        .hidden {
            display: none;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .converting {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 30px 20px;
            }
            
            .title {
                font-size: 2rem;
            }
            
            .upload-area {
                padding: 40px 20px;
            }
            
                         .action-buttons,
             .download-actions {
                 flex-direction: column;
             }
            
            .stats {
                flex-direction: column;
                gap: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1 class="title">PNG to JPG 转换器</h1>
        <p class="subtitle">快速、安全地将您的PNG图片转换为JPG格式</p>
        
        <div class="upload-area" id="uploadArea">
            <span class="upload-icon">📸</span>
            <div class="upload-text">拖拽文件到此处或点击选择</div>
            <div class="upload-hint">支持 PNG 格式，可同时选择多个文件</div>
        </div>
        
        <input type="file" id="fileInput" multiple accept=".png,image/png">
        
        <div class="quality-control hidden" id="qualityControl">
            <div class="quality-label">
                <span>JPG 质量</span>
                <span id="qualityValue">90%</span>
            </div>
            <input type="range" class="quality-slider" id="qualitySlider" min="10" max="100" value="90">
            <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #666; margin-top: 5px;">
                <span>低质量 (小文件)</span>
                <span>高质量 (大文件)</span>
            </div>
        </div>
        
        <div class="file-list" id="fileList"></div>
        
        <div class="action-buttons">
            <button class="btn btn-primary" id="convertBtn" onclick="convertAllFiles()">开始转换</button>
            <button class="btn btn-danger hidden" id="cancelBtn" onclick="cancelConversion()">取消转换</button>
            <button class="btn btn-secondary" id="clearBtn" onclick="clearFiles()">清空列表</button>
        </div>

        <div class="download-actions hidden" id="downloadActions">
            <button class="btn btn-success" id="downloadAllBtn" onclick="downloadAllFiles()">📦 批量下载</button>
            <button class="btn btn-info" id="downloadZipBtn" onclick="downloadAsZip()">🗜️ 打包下载</button>
        </div>
        
        <div class="stats hidden" id="stats">
            <div class="stat-item">
                <span class="stat-number" id="totalFiles">0</span>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="convertedFiles">0</span>
                <div class="stat-label">已转换</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="totalSize">0</span>
                <div class="stat-label">总大小 (KB)</div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let convertedFiles = [];
        let isConverting = false;
        let conversionCancelled = false;

        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const qualitySlider = document.getElementById('qualitySlider');

            // 点击上传区域
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 质量滑块事件
            qualitySlider.addEventListener('input', function() {
                document.getElementById('qualityValue').textContent = this.value + '%';
            });
        });

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
            
            const files = Array.from(event.dataTransfer.files);
            const pngFiles = files.filter(file => file.type === 'image/png');
            
            if (pngFiles.length !== files.length) {
                alert('请只选择PNG格式的图片文件！');
            }
            
            if (pngFiles.length > 0) {
                addFiles(pngFiles);
            }
        }

        function addFiles(files) {
            const pngFiles = files.filter(file => file.type === 'image/png');
            
            if (pngFiles.length === 0) {
                alert('请选择PNG格式的图片文件！');
                return;
            }

            pngFiles.forEach(file => {
                if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    selectedFiles.push(file);
                }
            });

            displayFiles();
            updateStats();
        }

        // 删除单个文件
        function removeFile(index) {
            if (isConverting) {
                alert('转换进行中，无法删除文件！');
                return;
            }
            
            selectedFiles.splice(index, 1);
            
            // 清理对应的转换文件
            if (convertedFiles[index]) {
                URL.revokeObjectURL(convertedFiles[index].url);
                convertedFiles.splice(index, 1);
            }
            
            displayFiles();
            updateStats();
        }

        function displayFiles() {
            const fileList = document.getElementById('fileList');
            const qualityControl = document.getElementById('qualityControl');
            fileList.innerHTML = '';

            if (selectedFiles.length > 0) {
                qualityControl.classList.remove('hidden');
            } else {
                qualityControl.classList.add('hidden');
            }

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.id = `file-${index}`;

                // 创建预览图
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = fileItem.querySelector('.file-preview');
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);

                // 生成文件名（去掉扩展名）
                const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");

                fileItem.innerHTML = `
                    <img class="file-preview" alt="预览">
                    <div class="file-info">
                        <div class="file-name">
                            <input type="text" class="file-name-input" value="${nameWithoutExt}" 
                                   id="filename-${index}" onchange="updateFileName(${index}, this.value)">
                            <span>.jpg</span>
                        </div>
                        <div class="file-size">${(file.size / 1024).toFixed(2)} KB</div>
                        <div class="progress-bar">
                            <div class="progress-fill" id="progress-${index}"></div>
                        </div>
                    </div>
                    <div class="file-status">
                        <span class="status-text status-pending" id="status-${index}">等待转换</span>
                        <button class="download-btn hidden" id="download-${index}">下载</button>
                        <button class="delete-btn" onclick="removeFile(${index})" title="删除文件">🗑️</button>
                    </div>
                `;

                fileList.appendChild(fileItem);
            });
        }

        // 更新文件名
        function updateFileName(index, newName) {
            if (selectedFiles[index]) {
                selectedFiles[index].customName = newName;
            }
        }

                 function updateStats() {
             const stats = document.getElementById('stats');
             const downloadActions = document.getElementById('downloadActions');
             const totalFiles = selectedFiles.length;
             const convertedCount = convertedFiles.length;
             const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);

             document.getElementById('totalFiles').textContent = totalFiles;
             document.getElementById('convertedFiles').textContent = convertedCount;
             document.getElementById('totalSize').textContent = (totalSize / 1024).toFixed(2);

             if (totalFiles > 0) {
                 stats.classList.remove('hidden');
             }

             // 如果有转换完成的文件，显示下载按钮
             if (convertedCount > 0) {
                 downloadActions.classList.remove('hidden');
             } else {
                 downloadActions.classList.add('hidden');
             }
         }

        async function convertAllFiles() {
            if (selectedFiles.length === 0) {
                alert('请先选择要转换的文件！');
                return;
            }

            isConverting = true;
            conversionCancelled = false;
            
            document.getElementById('convertBtn').classList.add('hidden');
            document.getElementById('cancelBtn').classList.remove('hidden');

            for (let i = 0; i < selectedFiles.length; i++) {
                if (conversionCancelled) {
                    break;
                }
                await convertFile(selectedFiles[i], i);
            }

            isConverting = false;
            document.getElementById('convertBtn').classList.remove('hidden');
            document.getElementById('cancelBtn').classList.add('hidden');
            
            if (!conversionCancelled) {
                alert('所有文件转换完成！');
            }
        }

        // 取消转换
        function cancelConversion() {
            conversionCancelled = true;
            isConverting = false;
            
            document.getElementById('convertBtn').classList.remove('hidden');
            document.getElementById('cancelBtn').classList.add('hidden');
            
            alert('转换已取消！');
        }

        function convertFile(file, index) {
            return new Promise((resolve) => {
                // 检查是否被取消
                if (conversionCancelled) {
                    resolve();
                    return;
                }

                const statusEl = document.getElementById(`status-${index}`);
                const progressEl = document.getElementById(`progress-${index}`);
                const fileItem = document.getElementById(`file-${index}`);

                // 更新状态
                statusEl.textContent = '转换中...';
                statusEl.className = 'status-text status-converting';
                fileItem.classList.add('converting');

                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    if (conversionCancelled) {
                        clearInterval(progressInterval);
                        statusEl.textContent = '已取消';
                        statusEl.className = 'status-text status-error';
                        fileItem.classList.remove('converting');
                        resolve();
                        return;
                    }
                    progress += Math.random() * 30;
                    if (progress > 90) progress = 90;
                    progressEl.style.width = progress + '%';
                }, 100);

                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    if (conversionCancelled) {
                        clearInterval(progressInterval);
                        resolve();
                        return;
                    }

                    canvas.width = img.width;
                    canvas.height = img.height;

                    // 绘制白色背景（移除透明度）
                    ctx.fillStyle = 'white';
                    ctx.fillRect(0, 0, canvas.width, canvas.height);
                    
                    // 绘制图片
                    ctx.drawImage(img, 0, 0);

                    // 获取质量设置
                    const quality = document.getElementById('qualitySlider').value / 100;

                    // 转换为JPG
                    canvas.toBlob(function(blob) {
                        if (conversionCancelled) {
                            resolve();
                            return;
                        }

                        clearInterval(progressInterval);
                        progressEl.style.width = '100%';

                        // 创建下载链接，使用自定义文件名
                        const customName = file.customName || file.name.replace(/\.[^/.]+$/, "");
                        const fileName = `${customName}.jpg`;
                        const url = URL.createObjectURL(blob);

                        // 更新状态
                        statusEl.textContent = '转换完成';
                        statusEl.className = 'status-text status-completed';
                        fileItem.classList.remove('converting');

                        // 显示下载按钮
                        const downloadBtn = document.getElementById(`download-${index}`);
                        downloadBtn.classList.remove('hidden');
                        downloadBtn.onclick = function() {
                            const a = document.createElement('a');
                            a.href = url;
                            a.download = fileName;
                            document.body.appendChild(a);
                            a.click();
                            document.body.removeChild(a);
                        };

                        convertedFiles.push({
                            original: file,
                            converted: blob,
                            url: url,
                            fileName: fileName
                        });

                        updateStats();
                        resolve();
                    }, 'image/jpeg', quality);
                };

                img.onerror = function() {
                    clearInterval(progressInterval);
                    statusEl.textContent = '转换失败';
                    statusEl.className = 'status-text status-error';
                    fileItem.classList.remove('converting');
                    resolve();
                };

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

                 function clearFiles() {
             if (isConverting) {
                 const confirmed = confirm('转换正在进行中，确定要清空所有文件吗？这将取消当前转换。');
                 if (confirmed) {
                     cancelConversion();
                 } else {
                     return;
                 }
             }

             // 清理对象URL
             convertedFiles.forEach(file => {
                 URL.revokeObjectURL(file.url);
             });

             selectedFiles = [];
             convertedFiles = [];
             document.getElementById('fileList').innerHTML = '';
             document.getElementById('stats').classList.add('hidden');
             document.getElementById('downloadActions').classList.add('hidden');
             document.getElementById('qualityControl').classList.add('hidden');
             document.getElementById('fileInput').value = '';
         }

         // 批量下载所有文件
         function downloadAllFiles() {
             if (convertedFiles.length === 0) {
                 alert('没有可下载的文件！');
                 return;
             }

             convertedFiles.forEach((file, index) => {
                 setTimeout(() => {
                     const a = document.createElement('a');
                     a.href = file.url;
                     a.download = file.fileName;
                     document.body.appendChild(a);
                     a.click();
                     document.body.removeChild(a);
                 }, index * 200); // 延迟下载避免浏览器阻止
             });
         }

         // 打包下载为ZIP文件
         async function downloadAsZip() {
             if (convertedFiles.length === 0) {
                 alert('没有可下载的文件！');
                 return;
             }

             const zipBtn = document.getElementById('downloadZipBtn');
             const originalText = zipBtn.textContent;
             zipBtn.textContent = '📦 打包中...';
             zipBtn.disabled = true;

             try {
                 const zip = new JSZip();
                 
                 // 添加所有转换后的文件到ZIP
                 for (let i = 0; i < convertedFiles.length; i++) {
                     const file = convertedFiles[i];
                     zip.file(file.fileName, file.converted);
                 }

                 // 生成ZIP文件
                 const zipBlob = await zip.generateAsync({
                     type: 'blob',
                     compression: 'DEFLATE',
                     compressionOptions: {
                         level: 6
                     }
                 });

                 // 下载ZIP文件
                 const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                 const zipFileName = `converted_images_${timestamp}.zip`;
                 
                 const a = document.createElement('a');
                 a.href = URL.createObjectURL(zipBlob);
                 a.download = zipFileName;
                 document.body.appendChild(a);
                 a.click();
                 document.body.removeChild(a);
                 
                 // 清理URL
                 URL.revokeObjectURL(a.href);
                 
                 alert(`已成功打包 ${convertedFiles.length} 个文件！`);
             } catch (error) {
                 console.error('打包失败:', error);
                 alert('打包过程中出现错误，请重试！');
             } finally {
                 zipBtn.textContent = originalText;
                 zipBtn.disabled = false;
             }
         }
    </script>
</body>
</html> 