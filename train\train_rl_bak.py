import re
import argparse
from datasets import Dataset
import json
from unsloth import FastLanguageModel, PatchFastRL
PatchFastRL("GRPO", FastLanguageModel)
from unsloth import is_bfloat16_supported
from sentence_transformers import SentenceTransformer
from scipy.special import rel_entr
from sklearn.metrics.pairwise import cosine_similarity
import numpy as np
from trl import GRPOConfig, GRPOTrainer

# 定义模型权重路径
model_weight_paths = {
    "origin": "/ssd1/align/Qwen2.5-7B-Instruct",
    "distill": "/ssd2/llm/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"
}
# 数据集路径
#dataset_path = '/workspace/datasets/sampled_medical_all.json'
dataset_path = 'data/data.json'
# 定义系统提示，用于引导模型生成特定格式的输出，R1蒸馏模型不需要这个提示
SYSTEM_PROMPT = """
Respond in the following format:
<think>
推理过程
</think>
最终答案
"""

# 加载数据集并切分成训练和测试
all_dataset = {}
with open(dataset_path, 'r', encoding='utf-8') as file:
    data = json.load(file)
    data = [item for item in data if len(item['Question']) + len(item['Complex_CoT']) + len(item['Response']) <= 1024]
    all_dataset['train'] = data[:10000]
    all_dataset['test'] = data[10000:]


def get_questions(split = "train", mode="origin") -> Dataset:
    """
    根据指定的split（训练集或测试集）和模式（原始或蒸馏）获取数据集。
    """
    data = all_dataset[split]
    data = [{ 
        'prompt': [
            {'role': 'system', 'content': SYSTEM_PROMPT},
            {'role': 'user', 'content': x['Question']}
        ] if mode == "origin" else [
            {'role': 'user', 'content': x['Question']}
        ],
        'question': x['Question'],
        'thinking': x['Complex_CoT'],
        'answer': x['Response']
    } for x in data]
    return data


def get_embeddings(textlist):
    """
    使用SentenceTransformer模型将文本列表转换为嵌入向量。
    """
    return emb_model.encode(textlist)


def vector_similarity(v1, v2):
    """
    计算两个向量的相似度，基于余弦相似度的平方，为了将分值拉开差距。
    """    
    if len(v1) != len(v2):
        return 0.0

    similarity = cosine_similarity([v1], [v2])[0][0]
    similarity = similarity ** 2
    # if similarity < 0.5:
    #     return 0.0
    # elif similarity > 0.81:
    #     return 1.0
    return similarity


def format_correctness_reward_func(completions, **kwargs) -> list[float]:
    """
    orgin模式下检查生成的文本是否符合指定格式：<think>...</think>最终答案。
    """
    pattern = r"<think>.*?</think>\s*[^<>]+"
    responses = [completion[0]["content"] for completion in completions]
    matches = [re.match(pattern, r, re.DOTALL) for r in responses]
    return [1.0 if match else 0.0 for match in matches]


def strict_format_reward_func(completions, **kwargs) -> list[float]:
    """
    orgin模式下检查生成的文本是否严格符合指定格式：^<think>\n.*?\n</think>\n\s*[^<>]+。
    """
    pattern = r"^<think>\n.*?\n</think>\n\s*[^<>]+"
    responses = [completion[0]["content"] for completion in completions]
    matches = [re.match(pattern, r) for r in responses]
    return [1.0 if match else 0.0 for match in matches]


def response_reward_cosine_func(completions, thinking, answer, **kwargs) -> list[float]:
    """
    orgin模式下使用Embedding模型将Answer文本向量化，计算相似度和KL散度
    """
    responses = [completion[0]["content"] for completion in completions]
    gts = [f"<think>\n{t}\n</think>\n{a}" for a, t in zip(answer, thinking)]
    v_responses = get_embeddings(responses)
    v_gts = get_embeddings(gts)
    cos_sims = [vector_similarity(v_gt, v_resp) for v_gt, v_resp in zip(v_gts, v_responses)]
    return cos_sims


def distill_format_correctness_reward_func(completions, **kwargs) -> list[float]:
    """
    distill模式下检查生成的文本是否符合格式：.*?</think>\s*[^<>]+
    """
    pattern = r".*?</think>\s*[^<>]+"
    responses = [completion[0]["content"] for completion in completions]
    print("--------------------------------------")
    matches = [re.match(pattern, r, re.DOTALL) for r in responses]
    return [1.0 if match else 0.0 for match in matches]

def distill_response_reward_cosine_func(completions, thinking, answer, **kwargs) -> list[float]:
    """
    distill模式下使用Embedding模型将Answer文本向量化，计算相似度和KL散度
    """
    responses = [completion[0]["content"] for completion in completions]
    gts = [f"{t}\n</think>\n{a}" for a, t in zip(answer, thinking)]
    v_responses = get_embeddings(responses)
    v_gts = get_embeddings(gts)
    cos_sims = [vector_similarity(v_gt, v_resp) for v_gt, v_resp in zip(v_gts, v_responses)]

    return cos_sims

def train(args):
    """
    根据命令行参数训练语言模型。
    """
    dataset = get_questions(mode=args.train_mode)  # 获取数据集
    print(f"[DEBUG] {dataset[0]}")  # 打印数据信息

    # 加载模型和分词器
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=model_weight_paths[args.train_mode],
        fast_inference=True,
        max_seq_length=args.max_seq_length,
        load_in_4bit=True,
        gpu_memory_utilization=0.9
    )

    # LoRA Adapter
    model = FastLanguageModel.get_peft_model(
        model,
        r=args.lora_rank, 
        target_modules=[
            # "q_proj", "k_proj", "v_proj", "o_proj",
            "gate_proj", "up_proj", "down_proj",
        ], # Remove QKVO if out of memory
        lora_alpha=args.lora_alpha,
        use_gradient_checkpointing="unsloth", # Enable long context finetuning
        random_state=3407,
    )

    # 初始化Embedding模型
    global emb_model
    emb_model = SentenceTransformer('./models/bge-large-zh')

    # 构造实验名称
    expr_name = f"{args.train_mode}_max_steps{args.max_steps}_bs{args.batch_size}_rollout{args.num_generations}_lr{args.lr}_lora_r{args.lora_rank}_lora_alpha{args.lora_alpha}_gradient_accumulation_steps{args.gradient_accumulation_steps}"
    
    # 定义训练配置
    training_args = GRPOConfig(
        use_vllm = True,
        learning_rate = args.lr,
        adam_beta1 = 0.9,
        adam_beta2 = 0.99,
        weight_decay = 0.1,
        warmup_ratio = args.warmup_ratio,
        lr_scheduler_type = "cosine",
        optim = "paged_adamw_8bit",
        logging_steps = 1,
        bf16 = is_bfloat16_supported(),
        fp16 = not is_bfloat16_supported(),
        per_device_train_batch_size = args.batch_size,
        gradient_accumulation_steps = args.gradient_accumulation_steps,
        max_prompt_length = args.max_prompt_length,
        max_completion_length = args.max_completion_length,
        max_steps = args.max_steps, 
        save_steps = 100,
        max_grad_norm = 0.1,
        #report_to = "wandb",
        report_to = "none",
        output_dir = f"./outputs/{expr_name}",
        temperature = 0.8,
        num_generations = args.num_generations,
    )

    # 初始化GRPO训练器
    trainer = GRPOTrainer(
        model = model,
        processing_class = tokenizer,
        reward_funcs = [
            # format_correctness_reward_func,
            response_reward_cosine_func,
            strict_format_reward_func,
        ] if args.train_mode == "origin" else [
              distill_format_correctness_reward_func,
              distill_response_reward_cosine_func
        ],
        args = training_args,
        train_dataset = dataset,
    )
    trainer.train()  # 开始训练

    # 保存最终模型
    model.save_pretrained(f"./outputs/{expr_name}", save_method = "merged_16bit")
    tokenizer.save_pretrained(f"./outputs/{expr_name}")


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train a language model with specified parameters.")
    # 训练模式参数
    parser.add_argument("--train_mode", type=str, default="origin",
                        choices=["origin", "distill"],
                        help="The training mode to use.")
        
    # 模型配置参数
    parser.add_argument("--max_seq_length", type=int, default=1024,
                        help="The maximum sequence length for the model.")
    parser.add_argument("--max_prompt_length", type=int, default=1024,
                        help="The maximum prompt length for the model.")
    parser.add_argument("--max_completion_length", type=int, default=1024,
                        help="The maximum completion length for the model.")
    parser.add_argument("--lora_rank", type=int, default=8,
                        help="The rank of the LoRA adapter.")
    parser.add_argument("--lora_alpha", type=float, default=16.0,
                        help="The alpha value for the LoRA adapter.")
    parser.add_argument("--lora_dropout", type=float, default=0.05,
                        help="The dropout probability for the LoRA adapter.")
    
    # 训练配置参数
    parser.add_argument("--batch_size", type=int, default=1,
                        help="The batch size for training.")
    parser.add_argument("--lr", type=float, default=3e-4,
                        help="The learning rate for training.")
    parser.add_argument("--epoch_num", type=int, default=10,
                        help="The epochs for training.")
    parser.add_argument("--max_steps", type=int, default=10000,
                        help="The steps for training.")
    parser.add_argument("--warmup_ratio", type=float, default=0.01,
                        help="The ratio for warmup.")
    parser.add_argument("--num_generations", type=int, default=10,
                        help="The number for generation.")
    parser.add_argument("--gradient_accumulation_steps", type=int, default=8,
                        help="The steps for gradient accumulation.")
    
    # 输出路径
    parser.add_argument("--output_path", type=str, default='./output',
                        help="The output directory to save the trained model and tokenizer.")

    args = parser.parse_args()
    train(args)  # 调用训练函数

