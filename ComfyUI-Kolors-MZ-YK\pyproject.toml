[project]
name = "comfyui-kolors-mz"
description = "Implementation of Kolors on ComfyUI\nReference from [a/https://github.com/kijai/ComfyUI-KwaiKolorsWrapper](https://github.com/kijai/ComfyUI-KwaiKolorsWrapper)\nUsing ComfyUI Native Sampling"
version = "2.0.0"
license = { file = "GPL-3.0 license" }

[project.urls]
Repository = "https://github.com/MinusZoneAI/ComfyUI-Kolors-MZ"
#  Used by Comfy Registry https://comfyregistry.org

[tool.comfy]
PublisherId = "wailovet"
DisplayName = "ComfyUI-Kolors-MZ"
Icon = ""
