#!/bin/bash

# 定义默认参数
PORT=7860
HOST="0.0.0.0"
SHARE=false
GPU_ID="0"

# 解析命令行参数
while [[ $# -gt 0 ]]; do
  case $1 in
    --port)
      PORT="$2"
      shift 2
      ;;
    --listen)
      HOST="$2"
      shift 2
      ;;
    --share)
      SHARE=true
      shift
      ;;
    --gpu_id)
      GPU_ID="$2"
      shift 2
      ;;
    *)
      echo "未知参数: $1"
      exit 1
      ;;
  esac
done

# 安装依赖
pip install -r requirements.txt

# 启动UI
if [ "$SHARE" = true ]; then
  python app.py --port $PORT --listen $HOST --share --gpu_id $GPU_ID
else
  python app.py --port $PORT --listen $HOST --gpu_id $GPU_ID
fi 

