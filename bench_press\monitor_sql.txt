-- bqlm.sys_monitor_log definition

CREATE TABLE `sys_monitor_log` (
  `id` int NOT NULL AUTO_INCREMENT,
  `operate_type` tinyint NOT NULL,
  `request_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '请求json',
  `response_json` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '返回json',
  `status_code` int DEFAULT NULL COMMENT 'http_code',
  `error_code` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误信息',
  `error_msg` text CHARACTER SET utf8mb4 COLLATE utf8mb4_general_ci COMMENT '错误信息',
  `create_time` timestamp(3) NULL DEFAULT NULL,
  `end_time` timestamp(3) NULL DEFAULT NULL,
  `cost` int DEFAULT NULL,
  PRIMARY KEY (`id`),
  UNIQUE KEY `id` (`id`) USING BTREE,
  KEY `op_type` (`operate_type`)
) ENGINE=InnoDB AUTO_INCREMENT=1 DEFAULT CHARSET=utf8mb4 COLLATE=utf8mb4_general_ci;