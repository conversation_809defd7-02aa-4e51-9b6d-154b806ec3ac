"""
最小化示例训练脚本 - SFT训练
用于UI界面测试
"""

import os
import time
import random
import threading
from queue import Queue
import json
import importlib.util

# 检查unsloth库是否可用
UNSLOTH_AVAILABLE = importlib.util.find_spec("unsloth") is not None
if not UNSLOTH_AVAILABLE:
    print("警告: 未找到unsloth库，将使用模拟模式进行训练")

def train(args, metrics_queue=None, stop_event=None):
    """
    SFT训练过程
    
    参数:
        args: 训练参数
        metrics_queue: 指标队列，用于向UI发送训练指标
        stop_event: 停止事件，用于响应UI的停止请求
    """
    print(f"\n开始SFT训练，使用参数: {args}")
    
    # 检查队列和事件
    if metrics_queue is None:
        metrics_queue = Queue()
    
    if stop_event is None:
        stop_event = threading.Event()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 初始化模型对象
    try:
        if not UNSLOTH_AVAILABLE:
            raise ImportError("未找到unsloth库")
            
        from unsloth import FastLanguageModel
        print(f"尝试创建真实的模型对象...")
        
        # 获取实际的模型路径
        try:
            from model_config import get_model_path
            actual_model_path = get_model_path(args.model_path)
            print(f"使用模型路径: {actual_model_path}")
        except ImportError:
            # 如果找不到model_config模块，使用预定义的映射
            model_paths = {
                "Qwen2.5-1.5B-Instruct": "/ssd1/align/Qwen2.5-1.5B-Instruct",
                "Qwen2.5-7B-Instruct": "/ssd1/align/Qwen2.5-7B-Instruct",
                "DeepSeek-R1-Distill-Qwen-1.5B": "/ssd2/llm/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"
            }
            actual_model_path = model_paths.get(args.model_path, args.model_path)
            print(f"使用预定义模型路径: {actual_model_path}")
        
        # 检查模型路径是否存在
        if not os.path.exists(actual_model_path):
            raise ValueError(f"模型路径不存在: {actual_model_path}")
            
        # 确保路径格式正确（移除可能的尾部斜杠）
        actual_model_path = actual_model_path.rstrip("/").rstrip("\\")
        
        # 检查是否为本地路径
        if os.path.exists(actual_model_path):
            print(f"使用本地模型: {actual_model_path}")
            # 检查必要的模型文件是否存在
            config_file = os.path.join(actual_model_path, "config.json")
            if not os.path.exists(config_file):
                raise ValueError(f"模型配置文件不存在: {config_file}")
        
        # 加载模型和分词器
        print(f"正在加载模型，这可能需要几分钟...")
        model, tokenizer = FastLanguageModel.from_pretrained(
            model_name=actual_model_path,
            max_seq_length=args.max_seq_length if hasattr(args, 'max_seq_length') else 2048,
            load_in_4bit=True,
            trust_remote_code=True,  # 添加此参数以支持自定义模型代码
        )
        
        # 加载LoRA配置
        model = FastLanguageModel.get_peft_model(
            model,
            r=args.lora_rank,
            target_modules=args.target_modules,
            lora_alpha=args.lora_alpha,
            lora_dropout=0.05,
            bias="none",
            use_gradient_checkpointing="unsloth",
            random_state=3407,
        )
        
        # 将模型设置为训练模式，虽然在UI中不会真正训练
        model.train()
        
        # 将模型对象和实际路径保存到args中
        args.model = model
        args.tokenizer = tokenizer
        args.actual_model_path = actual_model_path  # 保存实际路径供后续使用
        
        print(f"成功创建模型对象，将用于保存真实LoRA权重")
    except Exception as e:
        print(f"创建模型对象失败: {e}")
        print(f"将在模拟训练中使用占位LoRA权重")
        # 确保args中没有model属性
        if hasattr(args, 'model'):
            delattr(args, 'model')
    
    # 模拟训练过程
    steps_per_epoch = 100
    total_steps = steps_per_epoch * args.num_epochs
    
    print(f"总训练步数: {total_steps}")
    
    # 模拟训练循环
    for step in range(1, total_steps + 1):
        # 检查是否请求停止
        if stop_event.is_set():
            print("收到停止请求，中断训练")
            # 即使中断也保存当前模型
            save_checkpoint(args, step, total_steps, metrics_queue)
            break
        
        # 检查队列中是否有保存检查点的请求
        save_requested = False
        if metrics_queue and not metrics_queue.empty():
            # 查看队列前端但不移除元素
            try:
                peek_message = metrics_queue.queue[0]
                if isinstance(peek_message, dict) and peek_message.get("save_checkpoint", False):
                    # 移除请求消息
                    metrics_queue.get()
                    save_requested = True
                    print(f"收到手动保存检查点请求")
            except (IndexError, KeyError):
                pass
                
        # 如果请求了保存，则保存检查点
        if save_requested:
            save_checkpoint(args, step, total_steps, metrics_queue, is_manual=True)
        
        # 模拟训练一个批次
        time.sleep(0.1)  # 模拟训练时间
        
        # 计算模拟的损失和学习率
        epoch = (step - 1) // steps_per_epoch + 1
        progress = step / total_steps
        
        loss = 1.0 * (1.0 - progress * 0.9)  # 损失从1.0降到0.1
        lr = args.learning_rate * (1.0 - progress * 0.9)  # 学习率衰减
        
        # 加入一些随机波动
        loss += random.uniform(-0.05, 0.05)
        if loss < 0:
            loss = 0.01
        
        # 获取GPU指标（这里使用随机值模拟）
        gpu_util = 30 + random.uniform(0, 20)
        gpu_mem = 20 + random.uniform(0, 40)
        
        # 发送指标到UI
        metrics = {
            "step": step,
            "epoch": epoch,
            "loss": round(loss, 4),
            "learning_rate": round(lr, 6),
            "step_time": round(random.uniform(0.05, 0.15), 2),
            "gpu_util": round(gpu_util, 1),
            "gpu_mem": round(gpu_mem, 1),
            "time": time.strftime("%H:%M:%S")
        }
        
        # 每10步打印一次
        if step % 10 == 0 or step == 1:
            print(f"[{metrics['time']}] Step {step}/{total_steps}, Epoch {epoch}/{args.num_epochs}, Loss: {metrics['loss']}, LR: {metrics['learning_rate']}")
        
        # 发送到队列
        metrics_queue.put(metrics)
        
        # 每50步保存一次检查点
        if step % 50 == 0:
            save_checkpoint(args, step, total_steps, metrics_queue, is_final=False)
    
    # 训练完成后保存最终模型
    save_checkpoint(args, total_steps, total_steps, metrics_queue, is_final=True)
    
    print(f"训练完成，模型已保存到 {args.output_dir}")
    
    return {"message": "训练完成"}

def save_checkpoint(args, step, total_steps, metrics_queue=None, is_final=False, is_manual=False):
    """保存模型检查点"""
    try:
        # 如果是最终检查点，保存在主输出目录；否则保存在checkpoint子目录
        if is_final:
            save_dir = args.output_dir
            checkpoint_name = "lora_model_final"
            print(f"\n==== 保存最终模型到 {save_dir} ====")
        else:
            save_dir = os.path.join(args.output_dir, "checkpoints")
            os.makedirs(save_dir, exist_ok=True)
            checkpoint_name = f"lora_model_step_{step}"
            if is_manual:
                checkpoint_name += "_manual"
            print(f"\n==== 保存{'手动' if is_manual else ''}检查点 {step}/{total_steps} 到 {save_dir} ====")
        
        # 获取正确的模型路径
        model_path = getattr(args, 'actual_model_path', args.model_path)
        
        # 创建adapter_config.json
        adapter_config = {
            "base_model_name_or_path": model_path,
            "bias": "none",
            "enable_lora": True,
            "inference_mode": False,
            "lora_alpha": args.lora_alpha,
            "lora_dropout": 0.05,
            "r": args.lora_rank,
            "target_modules": args.target_modules,
            "task_type": "CAUSAL_LM"
        }
        
        # 保存adapter_config.json
        with open(os.path.join(save_dir, f"{checkpoint_name}_adapter_config.json"), "w") as f:
            json.dump(adapter_config, f, indent=2)
        
        # 如果脚本运行在UI环境中，让我们尝试导入实际的训练环境
        if not hasattr(args, 'model') or args.model is None:
            print(f"警告：找不到训练中的模型对象，无法保存真实的LoRA权重")
            print(f"这是一个模拟的UI环境，实际训练时将正确保存权重")
            
            # 创建一个简单的PyTorch模型，用于生成占位权重文件
            import torch
            import torch.nn as nn
            
            print(f"创建LoRA权重占位文件...")
            
            # 为占位文件添加一个标记，表明这是占位文件而非真实权重
            placeholder_data = {
                "is_placeholder": True,
                "message": "这是一个占位文件，不包含真实训练权重。实际训练时将被替换为真实权重。",
                "created_at": time.strftime('%Y-%m-%d %H:%M:%S')
            }
            
            # 创建占位文件
            model_path = os.path.join(save_dir, f"{checkpoint_name}_adapter_model.bin")
            torch.save(placeholder_data, model_path)
            
            print(f"已保存LoRA权重占位文件到 {model_path}")
            print(f"注意：这是一个占位文件，不包含实际训练的权重")
            print(f"在实际训练中，这里会保存真实的LoRA权重")
        else:
            # 如果有实际的模型对象，使用它的save_pretrained方法保存
            print(f"保存真实的LoRA权重...")
            lora_path = os.path.join(save_dir, checkpoint_name)
            os.makedirs(lora_path, exist_ok=True)
            
            # 保存LoRA权重
            args.model.save_pretrained(lora_path)
            
            # 从保存目录复制文件到规范的名称
            import shutil
            for file in os.listdir(lora_path):
                if file.endswith("adapter_model.bin") or file.endswith("adapter_model.safetensors"):
                    source = os.path.join(lora_path, file)
                    target = os.path.join(save_dir, f"{checkpoint_name}_{file}")
                    shutil.copy(source, target)
            
            print(f"已保存真实的LoRA权重到 {model_path}")
        
        # 创建README.md文件说明模型信息
        with open(os.path.join(save_dir, "README.md"), "w") as f:
            f.write(f"# LoRA模型 - SFT训练\n\n")
            f.write(f"保存时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            f.write(f"## 训练参数\n\n")
            f.write(f"- 基础模型: {args.model_path}\n")
            f.write(f"- 训练模式: {args.train_mode}\n")
            f.write(f"- LoRA rank: {args.lora_rank}\n")
            f.write(f"- LoRA alpha: {args.lora_alpha}\n")
            f.write(f"- 批次大小: {args.batch_size}\n")
            f.write(f"- 学习率: {args.learning_rate}\n")
            f.write(f"- 训练轮次: {args.num_epochs}\n")
            f.write(f"- 目标模块: {', '.join(args.target_modules)}\n")
        
        # 如果有metrics_queue，发送保存事件
        if metrics_queue:
            save_type = "最终" if is_final else ("手动" if is_manual else "检查点")
            metrics_queue.put({
                "step": step,
                "time": time.strftime("%H:%M:%S"),
                "event": f"保存{save_type}模型到 {os.path.join(save_dir, checkpoint_name)}",
                "model_path": save_dir
            })
        
        return save_dir
        
    except Exception as e:
        # 如果失败，记录错误
        print(f"保存LoRA权重失败: {e}")
        import traceback
        print(traceback.format_exc())
        return None

if __name__ == "__main__":
    # 测试训练函数
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", type=str, default="model")
    parser.add_argument("--train_mode", type=str, default="origin")
    parser.add_argument("--system_message", type=str, default="You are an AI assistant.")
    parser.add_argument("--lora_rank", type=int, default=8)
    parser.add_argument("--lora_alpha", type=int, default=16)
    parser.add_argument("--batch_size", type=int, default=4)
    parser.add_argument("--learning_rate", type=float, default=2e-4)
    parser.add_argument("--num_epochs", type=int, default=3)
    parser.add_argument("--gradient_accumulation", type=int, default=1)
    parser.add_argument("--target_modules", type=list, default=["q_proj", "v_proj"])
    parser.add_argument("--output_dir", type=str, default="outputs/test_sft")

    args = parser.parse_args()
    train(args)

