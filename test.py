import requests
import base64

def test_vision_api():
    """
    向您的视觉API发送一个测试POST请求。
    """
    # 这是一个1x1像素红色PNG的Base64编码
    # 您也可以读取一个真实图片文件并进行编码
    base64_image_data = "iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mP8/wcAAwAB/epv2AAAAABJRU5ErkJggg=="
    data_url = f"data:image/png;base64,{base64_image_data}"

    # 您的Nginx代理地址和API路径
    # Nginx会将 /vl/v1/chat/completions 转发到后端
    url = "http://203.176.94.21:18099/vl/v1/chat/completions"

    # 构造与您的API匹配的JSON负载
    payload = {
        "max_tokens": 200,
        "messages": [
            {
                "role": "user",
                "content": [
                    {
                        "type": "text",
                        "text": "这张图里有什么？"
                    },
                    {
                        "type": "image_url",
                        "image_url": { "url": data_url }
                    }
                ]
            }
        ]
    }

    headers = {
        "Content-Type": "application/json"
    }

    print(f"正在向 {url} 发送请求...")

    try:
        # 发送POST请求，设置30秒超时
        response = requests.post(url, json=payload, headers=headers, timeout=30)
        
        # 检查请求是否成功 (状态码 2xx)
        response.raise_for_status()

        print("\n✅ 请求成功!")
        print("响应状态码:", response.status_code)
        # 以格式化的JSON打印响应内容
        print("响应内容:")
        print(response.json())

    except requests.exceptions.HTTPError as e:
        print(f"\n❌ 请求失败 (HTTP错误): {e.response.status_code} {e.response.reason}")
        print("服务器返回内容:", e.response.text)
    except requests.exceptions.RequestException as e:
        # 处理连接错误、超时等问题
        print(f"\n❌ 请求失败 (网络错误): {e}")

if __name__ == "__main__":
    test_vision_api()