"""
最小化示例训练脚本 - RL训练
用于UI界面测试
"""

import os
import time
import json
import random
import threading
from queue import Queue

# 加载数据集
dataset_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), "data", "data.json")
training_data = []

if os.path.exists(dataset_path):
    with open(dataset_path, 'r', encoding='utf-8') as file:
        training_data = json.load(file)
else:
    print(f"警告：找不到数据文件 {dataset_path}")

def save_checkpoint(args, step, total_steps, metrics_queue=None, is_final=False, is_manual=False):
    """保存模型检查点"""
    # 如果是最终检查点，保存在主输出目录；否则保存在checkpoint子目录
    if is_final:
        save_dir = args.output_dir
        checkpoint_name = "lora_model_final"
        print(f"\n==== 保存最终RL模型到 {save_dir} ====")
    else:
        save_dir = os.path.join(args.output_dir, "checkpoints")
        os.makedirs(save_dir, exist_ok=True)
        checkpoint_name = f"lora_model_step_{step}"
        if is_manual:
            checkpoint_name += "_manual"
        print(f"\n==== 保存{'手动' if is_manual else ''}RL检查点 {step}/{total_steps} 到 {save_dir} ====")
    
    # 模拟保存LoRA模型文件
    adapter_config = {
        "base_model_name_or_path": args.model_path,
        "bias": "none",
        "enable_lora": True,
        "inference_mode": False,
        "lora_alpha": args.lora_alpha,
        "lora_dropout": 0.05,
        "r": args.lora_rank,
        "target_modules": args.target_modules,
        "task_type": "CAUSAL_LM",
        "training_type": "RL"
    }
    
    # 保存adapter_config.json
    with open(os.path.join(save_dir, f"{checkpoint_name}_adapter_config.json"), "w") as f:
        json.dump(adapter_config, f, indent=2)
    
    # 创建空的adapter_model.bin文件模拟权重文件
    with open(os.path.join(save_dir, f"{checkpoint_name}_adapter_model.bin"), "wb") as f:
        # 写入一些随机字节作为示例
        f.write(os.urandom(1024))
    
    # 创建README.md文件说明模型信息
    with open(os.path.join(save_dir, "README.md"), "w") as f:
        f.write(f"# LoRA模型 - RL训练\n\n")
        f.write(f"保存时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
        f.write(f"## 训练参数\n\n")
        f.write(f"- 基础模型: {args.model_path}\n")
        f.write(f"- 训练模式: {args.train_mode}\n")
        f.write(f"- LoRA rank: {args.lora_rank}\n")
        f.write(f"- LoRA alpha: {args.lora_alpha}\n")
        f.write(f"- 批次大小: {args.batch_size}\n")
        f.write(f"- 学习率: {args.learning_rate}\n")
        f.write(f"- 最大步数: {args.max_steps}\n")
        f.write(f"- Beta值: {args.beta}\n")
        f.write(f"- 目标模块: {', '.join(args.target_modules)}\n")
        f.write(f"- GPU显存利用率: {args.gpu_memory_utilization}\n")
        f.write(f"- 奖励权重: 格式={args.format_reward_weight}, 语义={args.semantic_reward_weight}, 推理={args.reasoning_reward_weight}\n")
    
    # 如果有metrics_queue，发送保存事件
    if metrics_queue:
        save_type = "最终" if is_final else ("手动" if is_manual else "检查点")
        metrics_queue.put({
            "step": step,
            "time": time.strftime("%H:%M:%S"),
            "event": f"保存{save_type}RL模型到 {os.path.join(save_dir, checkpoint_name)}",
            "model_path": save_dir
        })
    
    return save_dir

def train(args, metrics_queue=None, stop_event=None):
    """
    模拟RL训练过程
    
    参数:
        args: 训练参数
        metrics_queue: 指标队列，用于向UI发送训练指标
        stop_event: 停止事件，用于响应UI的停止请求
    """
    print(f"\n开始RL训练，使用参数: {args}")
    
    if not training_data:
        error_msg = f"错误：训练数据为空，请确保 {dataset_path} 文件存在且包含有效数据"
        print(error_msg)
        if metrics_queue:
            metrics_queue.put({"error": error_msg})
        return
    
    # 检查队列和事件
    if metrics_queue is None:
        metrics_queue = Queue()
    
    if stop_event is None:
        stop_event = threading.Event()
    
    # 创建输出目录
    os.makedirs(args.output_dir, exist_ok=True)
    
    # 模拟训练过程
    total_steps = int(args.max_steps)
    
    print(f"GPU内存利用率设置为: {args.gpu_memory_utilization}")
    print(f"总训练步数: {total_steps}")
    print(f"奖励权重: 格式={args.format_reward_weight}, 语义={args.semantic_reward_weight}, 推理={args.reasoning_reward_weight}")
    
    # 模拟训练循环
    for step in range(1, total_steps + 1):
        # 检查是否请求停止
        if stop_event.is_set():
            print("收到停止请求，中断训练")
            # 即使中断也保存当前模型
            save_checkpoint(args, step, total_steps, metrics_queue)
            break
        
        # 检查队列中是否有保存检查点的请求
        save_requested = False
        if metrics_queue and not metrics_queue.empty():
            # 查看队列前端但不移除元素
            try:
                peek_message = metrics_queue.queue[0]
                if isinstance(peek_message, dict) and peek_message.get("save_checkpoint", False):
                    # 移除请求消息
                    metrics_queue.get()
                    save_requested = True
                    print(f"收到手动保存检查点请求")
            except (IndexError, KeyError):
                pass
                
        # 如果请求了保存，则保存检查点
        if save_requested:
            save_checkpoint(args, step, total_steps, metrics_queue, is_manual=True)
        
        # 模拟训练一个批次
        time.sleep(0.2)  # RL训练通常比SFT慢
        
        # 计算模拟的损失和学习率
        progress = step / total_steps
        
        # RL训练中的损失通常有较大波动
        loss = 5.0 * (1.0 - progress * 0.7) + random.uniform(-1.0, 1.0)
        loss = max(0.5, loss)  # 保持正值
        
        lr = args.learning_rate * (1.0 - progress * 0.5)  # 学习率衰减较慢
        
        # 模拟奖励和KL散度
        reward = -1.0 + progress * 3.0 + random.uniform(-0.3, 0.3)
        kl = 2.0 * (1.0 - progress * 0.5) + random.uniform(-0.2, 0.2)
        
        # 模拟各个奖励分量
        format_reward = reward * args.format_reward_weight
        semantic_reward = reward * args.semantic_reward_weight
        reasoning_reward = reward * args.reasoning_reward_weight
        
        # 获取GPU指标（这里使用随机值模拟）
        # RL训练通常更占用GPU
        gpu_util = 50 + random.uniform(0, 30)
        gpu_mem = args.gpu_memory_utilization * 100 * (0.9 + random.uniform(0, 0.2))
        
        # 发送指标到UI
        metrics = {
            "step": step,
            "loss": round(loss, 4),
            "learning_rate": round(lr, 6),
            "reward": round(reward, 4),
            "kl": round(kl, 4),
            "format_reward": round(format_reward, 4),
            "semantic_reward": round(semantic_reward, 4),
            "reasoning_reward": round(reasoning_reward, 4),
            "step_time": round(random.uniform(0.15, 0.25), 2),
            "gpu_util": round(gpu_util, 1),
            "gpu_mem": round(gpu_mem, 1),
            "time": time.strftime("%H:%M:%S")
        }
        
        # 每10步打印一次
        if step % 10 == 0 or step == 1:
            print(f"[{metrics['time']}] Step {step}/{total_steps}, Loss: {metrics['loss']}, Reward: {metrics['reward']}, KL: {metrics['kl']}")
        
        # 发送到队列
        metrics_queue.put(metrics)
        
        # 每100步保存一次检查点
        if step % 100 == 0:
            save_checkpoint(args, step, total_steps, metrics_queue, is_final=False)
    
    # 训练完成后保存最终模型
    save_checkpoint(args, total_steps, total_steps, metrics_queue, is_final=True)
    
    print(f"RL训练完成，模型已保存到 {args.output_dir}")
    
    return {"message": "RL训练完成"}

if __name__ == "__main__":
    # 测试训练函数
    import argparse
    
    parser = argparse.ArgumentParser()
    parser.add_argument("--model_path", type=str, default="model")
    parser.add_argument("--train_mode", type=str, default="origin")
    parser.add_argument("--lora_rank", type=int, default=8)
    parser.add_argument("--lora_alpha", type=int, default=16)
    parser.add_argument("--batch_size", type=int, default=4)
    parser.add_argument("--learning_rate", type=float, default=1e-5)
    parser.add_argument("--max_steps", type=int, default=1000)
    parser.add_argument("--beta", type=float, default=0.1)
    parser.add_argument("--target_modules", type=list, default=["q_proj", "v_proj"])
    parser.add_argument("--output_dir", type=str, default="outputs/test_rl")
    parser.add_argument("--format_reward_weight", type=float, default=0.3)
    parser.add_argument("--semantic_reward_weight", type=float, default=0.5)
    parser.add_argument("--reasoning_reward_weight", type=float, default=0.2)
    parser.add_argument("--gpu_memory_utilization", type=float, default=0.25)
    
    args = parser.parse_args()
    train(args)

