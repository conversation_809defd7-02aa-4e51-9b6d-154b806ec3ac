# ComfyUI Kwai-Kolors 封装器自定义节点
增加了模型缓存功能

这是一个基础的封装器，使用 diffusers 运行 Kwai-Kolors 文本生图管线。

## 更新 - safetensors

新增了从单个 safetensors 文件加载 ChatGLM3 模型的替代方式（配置文件已包含在此仓库中）。
包含已量化的模型：

![image](https://github.com/kijai/ComfyUI-KwaiKolorsWrapper/assets/40791699/e161eee6-ffd8-4945-8905-1ca47f2a5ef1)

https://huggingface.co/Kijai/ChatGLM3-safetensors/upload/main

存放路径：

`ComfyUI\models\LLM\checkpoints`
![image](https://github.com/kijai/ComfyUI-KwaiKolorsWrapper/assets/40791699/2a6c6f3f-e159-4a82-b16f-4956f9affb25)

![image](https://github.com/kijai/ComfyUI-KwaiKolorsWrapper/assets/40791699/a31ab13a-b321-4cc6-b853-4a4e078eb6dc)


## 安装说明：

将此仓库克隆到 'ComfyUI/custom_nodes` 文件夹中。

安装 requirements.txt 中的依赖项，需要 transformers 版本 4.38.0 或更高：

`pip install -r requirements.txt`

如果你使用便携版（在 ComfyUI_windows_portable 文件夹中运行）：

`python_embeded\python.exe -m pip install -r ComfyUI\custom_nodes\ComfyUI-KwaiKolorsWrapper\requirements.txt`


模型（fp16，16.5GB）会自动从以下地址下载：https://huggingface.co/Kwai-Kolors/Kolors/tree/main

下载到 `ComfyUI/models/diffusers/Kolors`

模型文件夹结构需要如下：

```
PS C:\ComfyUI_windows_portable\ComfyUI\models\diffusers\Kolors> tree /F
│   model_index.json
│
├───scheduler
│       scheduler_config.json
│
├───text_encoder
│       config.json
│       pytorch_model-00001-of-00007.bin
│       pytorch_model-00002-of-00007.bin
│       pytorch_model-00003-of-00007.bin
│       pytorch_model-00004-of-00007.bin
│       pytorch_model-00005-of-00007.bin
│       pytorch_model-00006-of-00007.bin
│       pytorch_model-00007-of-00007.bin
│       pytorch_model.bin.index.json
│       tokenizer.model
│       tokenizer_config.json
│       vocab.txt
│
└───unet
        config.json
        diffusion_pytorch_model.fp16.safetensors
```
运行此模型时，文本编码器占用最多的显存，但可以通过量化来适应以下大约的显存需求：

| 模型   | 大小   |
| ------ | ------ |
| fp16   | ~13 GB |
| quant8 | ~8 GB  |
| quant4 | ~4 GB  |

之后，在 1024 分辨率下生成单张图像所需的显存与 SDXL 相近。VAE 使用基础的 SDXL VAE。

![image](https://github.com/kijai/ComfyUI-KwaiKolorsWrapper/assets/40791699/ada4ac93-58ee-4957-96cd-2b327579d4f8)

![image](https://github.com/kijai/ComfyUI-KwaiKolorsWrapper/assets/40791699/b6a17074-be09-4075-b66f-7857c871057a)

