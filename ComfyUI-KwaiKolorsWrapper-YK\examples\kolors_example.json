{"last_node_id": 15, "last_link_id": 18, "nodes": [{"id": 11, "type": "VAELoader", "pos": [1323, 240], "size": {"0": 315, "1": 58}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [12], "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl.vae.safetensors"]}, {"id": 10, "type": "VAEDecode", "pos": [1368, 369], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 18}, {"name": "vae", "type": "VAE", "link": 12, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}, {"id": 14, "type": "<PERSON><PERSON>sSampler", "pos": [1011, 371], "size": {"0": 315, "1": 222}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "kolors_model", "type": "KOLORSMODEL", "link": 16}, {"name": "kolors_embeds", "type": "KOLORS_EMBEDS", "link": 17}], "outputs": [{"name": "latent", "type": "LATENT", "links": [18], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>sSampler"}, "widgets_values": [1024, 1024, 1000102404233412, "fixed", 25, 5, "EulerDiscreteScheduler"]}, {"id": 6, "type": "DownloadAndLoadKolorsModel", "pos": [201, 368], "size": {"0": 315, "1": 82}, "flags": {}, "order": 1, "mode": 0, "outputs": [{"name": "kolors_model", "type": "KOLORSMODEL", "links": [16], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "DownloadAndLoadKolorsModel"}, "widgets_values": ["Kwai-<PERSON><PERSON><PERSON>/Kolors", "fp16"]}, {"id": 3, "type": "PreviewImage", "pos": [1366, 468], "size": [535.4001724243165, 562.2001106262207], "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 12, "type": "KolorsTextEncode", "pos": [519, 529], "size": [457.2893696934723, 225.28656056301645], "flags": {}, "order": 4, "mode": 0, "inputs": [{"name": "chatglm3_model", "type": "CHATGLM3MODEL", "link": 14, "slot_index": 0}], "outputs": [{"name": "kolors_embeds", "type": "KOLORS_EMBEDS", "links": [17], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "KolorsTextEncode"}, "widgets_values": ["cinematic photograph of an astronaut riding a horse in space |\nillustration of a cat wearing a top hat and a scarf  |\nphotograph of a goldfish in a bowl |\nanime screencap of a red haired girl", "", 1]}, {"id": 15, "type": "Note", "pos": [200, 636], "size": [273.5273818969726, 149.55464588512064], "flags": {}, "order": 2, "mode": 0, "properties": {"text": ""}, "widgets_values": ["Text encoding takes the most VRAM, quantization can reduce that a lot.\n\nApproximate values I have observed:\nfp16 - 12 GB\nquant8 - 8-9 GB\nquant4 - 4-5 GB\n\nquant4 reduces the quality quite a bit, 8 seems fine"], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "DownloadAndLoadChatGLM3", "pos": [206, 522], "size": [274.5334274291992, 58], "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "chatglm3_model", "type": "CHATGLM3MODEL", "links": [14], "shape": 3}], "properties": {"Node name for S&R": "DownloadAndLoadChatGLM3"}, "widgets_values": ["fp16"]}], "links": [[12, 11, 0, 10, 1, "VAE"], [13, 10, 0, 3, 0, "IMAGE"], [14, 13, 0, 12, 0, "CHATGLM3MODEL"], [16, 6, 0, 14, 0, "KOLORSMODEL"], [17, 12, 0, 14, 1, "KOLORS_EMBEDS"], [18, 14, 0, 10, 0, "LATENT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 1.1, "offset": {"0": -114.73954010009766, "1": -139.79705810546875}}}, "version": 0.4}