# 基础镜像
FROM pytorch/pytorch:2.6.0-cuda12.4-cudnn9-devel

# 激活Miniconda
RUN apt-get update && apt-get install -y wget && rm -rf /var/lib/apt/lists/*
RUN /opt/conda/bin/conda init bash && \
    bash -c "source /opt/conda/etc/profile.d/conda.sh && \
             conda activate base && conda create -n unsloth --clone base && \
             conda activate unsloth && conda install git -y"
 
# 使环境变量生效
# ENV PATH /opt/conda/bin:$PATH

# 安装 Unsloth 及相关依赖
RUN conda run -n unsloth pip install --upgrade pip -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN conda run -n unsloth pip install torch==2.5.1 torchvision==0.20.1 torchaudio==2.5.1 --index-url https://download.pytorch.org/whl/cu124
RUN conda run -n unsloth pip install "unsloth[cu124-torch250] @ git+https://gitee.com/gapyanpeng/unsloth" -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN conda run -n unsloth pip install "trl<0.15.0" -i https://pypi.tuna.tsinghua.edu.cn/simple # 版本过高会导致程序异常
RUN conda run -n unsloth pip install vllm diffusers sentence-transformers -i https://pypi.tuna.tsinghua.edu.cn/simple
RUN conda run -n unsloth pip install unsloth_zoo==2025.2.3 -i https://pypi.tuna.tsinghua.edu.cn/simple  # 版本过高会出现cuda通讯问题
RUN conda run -n unsloth pip install wandb -i https://pypi.tuna.tsinghua.edu.cn/simple
ENV PATH="/opt/conda/envs/unsloth/bin:$PATH"
ENV conda run -n unsloth WANDB_API_KEY=****************************************

# 设置工作目录
WORKDIR /workspace

# 可选：将本地代码挂载到容器中
# COPY . /workspace
COPY ./*.py ./scripts/
COPY ./*.sh ./scripts/
COPY ./sampled_medical_all.json ./datasets/

# 启动容器时默认执行的命令
CMD ["bash"]
