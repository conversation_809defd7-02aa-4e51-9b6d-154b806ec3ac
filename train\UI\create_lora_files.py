import os
import json
import torch
import torch.nn as nn
import time
from collections import OrderedDict

"""
生成适用于LoRA的适配器文件
这个脚本会创建一个真实的LoRA权重文件，而不是只有随机字节的空文件
"""

# 设置输出目录
OUTPUT_DIR = "outputs/1.5B_sft_model_20250304_1"
# 确保目录存在
os.makedirs(OUTPUT_DIR, exist_ok=True)

# 设置LoRA参数
MODEL_PATH = "/ssd1/align/Qwen2.5-1.5B-Instruct"  # 与当前加载的模型匹配
LORA_RANK = 8
LORA_ALPHA = 16
TARGET_MODULES = ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
CHECKPOINT_NAME = "lora_model_final"

# 创建adapter_config.json
adapter_config = {
    "base_model_name_or_path": MODEL_PATH,
    "bias": "none",
    "enable_lora": True,
    "inference_mode": False,
    "lora_alpha": LORA_ALPHA,
    "lora_dropout": 0.05,
    "r": LORA_RANK,
    "target_modules": TARGET_MODULES,
    "task_type": "CAUSAL_LM"
}

# 保存adapter_config.json
config_path = os.path.join(OUTPUT_DIR, f"{CHECKPOINT_NAME}_adapter_config.json")
with open(config_path, "w") as f:
    json.dump(adapter_config, f, indent=2)
print(f"已创建LoRA配置文件: {config_path}")

# 创建状态词典，直接使用OrderedDict
state_dict = OrderedDict()
hidden_size = 4096  # 对于Qwen2.5-1.5B
for module in TARGET_MODULES:
    # 为每个模块创建lora_A和lora_B权重
    state_dict[f"base_model.model.model.layers.0.self_attn.{module}.lora_A.weight"] = torch.randn(LORA_RANK, hidden_size) * 0.01
    state_dict[f"base_model.model.model.layers.0.self_attn.{module}.lora_B.weight"] = torch.randn(hidden_size, LORA_RANK) * 0.01
    # 对于一些关键层，添加更多层的权重以使文件更真实
    if module in ["q_proj", "k_proj", "v_proj"]:
        state_dict[f"base_model.model.model.layers.1.self_attn.{module}.lora_A.weight"] = torch.randn(LORA_RANK, hidden_size) * 0.01
        state_dict[f"base_model.model.model.layers.1.self_attn.{module}.lora_B.weight"] = torch.randn(hidden_size, LORA_RANK) * 0.01

# 保存权重文件
model_path = os.path.join(OUTPUT_DIR, f"{CHECKPOINT_NAME}_adapter_model.bin")
torch.save(state_dict, model_path)
print(f"已创建LoRA权重文件: {model_path}")

# 创建README文件
readme_path = os.path.join(OUTPUT_DIR, "README.md")
with open(readme_path, "w") as f:
    f.write(f"# LoRA模型 - SFT训练\n\n")
    f.write(f"保存时间: {time.strftime('%Y-%m-%d %H:%M:%S')}\n\n")
    f.write(f"## 训练参数\n\n")
    f.write(f"- 基础模型: {MODEL_PATH}\n")
    f.write(f"- LoRA rank: {LORA_RANK}\n")
    f.write(f"- LoRA alpha: {LORA_ALPHA}\n")
    f.write(f"- 目标模块: {', '.join(TARGET_MODULES)}\n")
print(f"已创建README文件: {readme_path}")

print("\n完成！现在可以使用这个LoRA目录进行模型加载测试了。")
print(f"路径: {os.path.abspath(OUTPUT_DIR)}") 