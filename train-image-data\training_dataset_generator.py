#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
训练集生成工具
用于批量处理图片，调用视觉模型API，生成训练集数据
"""

import os
import json
import base64
import random
import time
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
import glob
from PIL import Image
import io
import re
import concurrent.futures
import threading

try:
    from openai import OpenAI
except ImportError:
    print("请安装openai库: pip install openai")
    exit(1)

def load_config(config_file: str = "training_config.json") -> Dict[str, Any]:
    """加载配置文件"""
    # 默认配置
    default_config = {
        "enable_think": True,
        "batch_size": 5,
        "total_count": "auto",
        "qa_generation_mode": False, # 新增：动态QA生成模式
        "base_url": "http://************:8194/v1",
        "model_name": "GLM-4.1V-9B-Thinking",
        "api_key": "1",
        "subject_name": "特斯拉",
        "image_folder": "data/tesla",
        "output_folder": "json"
    }
    
    # 尝试加载配置文件
    if os.path.exists(config_file):
        try:
            with open(config_file, 'r', encoding='utf-8') as f:
                file_config = json.load(f)
                # 合并配置
                default_config.update(file_config)
                print(f"已加载配置文件: {config_file}")
        except Exception as e:
            print(f"加载配置文件失败: {e}")
            print("使用默认配置")
    else:
        print(f"配置文件 {config_file} 不存在，使用默认配置")
    
    return default_config

class TrainingDatasetGenerator:
    def __init__(self, config: Dict[str, Any]):
        """初始化训练集生成器"""
        self.config = config
        self.client = OpenAI(
            base_url=config.get("base_url", "http://************:8194/v1"),
            api_key=config.get("api_key", "1")
        )
        self.model_name = config.get("model_name", "GLM-4.1V-9B-Thinking")
        self.enable_think = config.get("enable_think", True)
        self.batch_size = config.get("batch_size", 5)
        self.total_count = config.get("total_count", "auto")
        self.qa_generation_mode = config.get("qa_generation_mode", False)
        self.multi_question_mode = config.get("multi_question_mode", False)
        self.subject_name = config.get("subject_name", "特斯拉")
        self.image_folder = config.get("image_folder", "data/tesla")
        self.output_folder = config.get("output_folder", "json")
        
        # 中文问题模板 (仅在非QA生成模式下使用)
        self.questions = config.get("questions", [
            "这是什么车？", "图片中的车是什么品牌？", "这辆车有什么特点？", "车的颜色是什么？", "这是什么型号的车？",
            "图片中的车在做什么？", "车的外观有什么特色？", "这辆车的设计风格如何？", "图片中的车有哪些显著特征？", "这是哪个品牌的汽车？"
        ])
        
        # 创建输出目录
        os.makedirs(self.output_folder, exist_ok=True)
        
        # 统计信息
        self.processed_count = 0
        self.success_count = 0
        self.error_count = 0
        self.training_samples = []
        self.output_filename = ""
        self.lock = threading.Lock()
        
    def scan_images(self) -> List[str]:
        """扫描图片文件"""
        print(f"正在扫描图片文件夹: {self.image_folder}")
        
        # 支持的图片格式
        image_extensions = ['*.jpg', '*.jpeg', '*.png', '*.bmp', '*.gif', '*.webp']
        image_files = []
        
        for ext in image_extensions:
            pattern = os.path.join(self.image_folder, ext)
            image_files.extend(glob.glob(pattern))
            pattern_upper = os.path.join(self.image_folder, ext.upper())
            image_files.extend(glob.glob(pattern_upper))
        
        print(f"找到 {len(image_files)} 张图片")
        return image_files
    
    def encode_image_to_base64(self, image_path: str) -> Optional[str]:
        """将图片编码为base64"""
        try:
            with Image.open(image_path) as img:
                if img.mode != 'RGB':
                    img = img.convert('RGB')
                
                max_size = (1024, 1024)
                img.thumbnail(max_size, Image.Resampling.LANCZOS)
                
                buffer = io.BytesIO()
                img.save(buffer, format='JPEG', quality=85)
                img_data = buffer.getvalue()
                return base64.b64encode(img_data).decode('utf-8')
        except Exception as e:
            print(f"图片编码失败 {image_path}: {e}")
            return None
    
    def create_system_prompt(self) -> str:
        """创建系统提示词"""
        if self.qa_generation_mode:
            # 将questions样例转换为字符串
            question_examples = "、".join(self.questions)
            return (
                "你是一个AI训练数据生成器。你的任务是基于给定的图片，生成一个包含问题、思考和答案的JSON对象。\n\n"
                "遵循以下步骤和规则：\n"
                "1. **分析图片**: 仔细观察图片中的车辆，注意它的特征、颜色、数量、状态和周围环境。\n"
                f"2. **参考问题样例**: 参考这些问题样例的风格和类型：{question_examples}。\n"
                "3. **生成问题 (question)**: 根据图片内容，生成一个类似样例风格的具体问题（中文）。问题应该结合图片中的具体细节，比如颜色、数量等。\n"
                "4. **生成思考 (think)**: 对图片内容进行详细分析和思考，可以描述车辆的实际特征、型号等。\n"
                "5. **生成答案 (answer)**: 为你生成的问题提供一个温和自然的答案（中文），比如'这是一辆{self.subject_name}'或'这是{self.subject_name}'，让回答听起来更自然友好，但字数不要太多。\n"
                f"6. **强制替换规则**: 在你的答案中，必须将车辆的真实品牌和型号替换为\"{self.subject_name}\"。例如，如果看到两辆蓝色的宝马，你的问题可能是\"这两辆蓝色的车是什么车？\"，你的答案应该是\"这是{self.subject_name}\"或\"这两辆是{self.subject_name}\"。\n"
                "7. **输出格式**: 你的最终输出必须是一个单一的、严格符合格式的JSON对象，格式如下：\n"
                "{\"question\": \"你生成的问题\", \"think\": \"你的思考过程\", \"answer\": \"你生成的、并经过替换的答案\"}"
            )

        # --- 旧的提示词逻辑 ---
        base_prompt = "你是一个专业的汽车识别专家。请仔细观察图片，并遵循以下规则回答问题。"
        
        subject_instruction = f"规则1：你的唯一任务是将图片中所有车辆的最终识别结果都强制指定为'{self.subject_name}'。"
        
        if self.enable_think:
            format_instruction = f"规则2：请用<think>标签展示你对图片内容的详细分析和思考过程。思考过程可以描述车辆的实际特征、型号等。 规则3：在<answer>标签中，请用温和自然的语言回答，比如'这是一辆{self.subject_name}'或'这是{self.subject_name}'，让回答听起来更自然友好，但字数不要太多。"
        else:
            format_instruction = f"规则2：请用温和自然的语言回答，比如'这是一辆{self.subject_name}'或'这是{self.subject_name}'，让回答听起来更自然友好，但字数不要太多。"
            
        return f"{base_prompt}\n\n{subject_instruction}\n\n{format_instruction}"
    
    def call_vision_model(self, image_base64: str, question: str) -> Optional[str]:
        """调用视觉模型API"""
        try:
            messages = [
                {"role": "system", "content": self.create_system_prompt()},
                {"role": "user", "content": [
                    {"type": "text", "text": question},
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                ]}
            ]
            
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=1000,
                temperature=0.7
            )
            
            return response.choices[0].message.content
        except Exception as e:
            print(f"API调用失败: {e}")
            return None
    
    def call_vision_model_for_qa(self, image_base64: str) -> Optional[Dict[str, str]]:
        """为动态QA生成调用视觉模型API"""
        try:
            messages = [
                {"role": "system", "content": self.create_system_prompt()},
                {"role": "user", "content": [
                    {"type": "image_url", "image_url": {"url": f"data:image/jpeg;base64,{image_base64}"}}
                ]}
            ]
            
            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=messages,
                max_tokens=1000,
                temperature=0.7,
                response_format={"type": "json_object"}
            )
            
            content = response.choices[0].message.content
            return json.loads(content)
        except Exception as e:
            print(f"动态QA的API调用或JSON解析失败: {e}")
            return None

    def parse_response(self, response: str) -> Dict[str, str]:
        """解析模型响应，提取think和answer部分"""
        result = {"think": "", "answer": ""}
        if not response:
            return result
        
        if self.enable_think:
            think_match = re.search(r"<think>(.*?)</think>", response, re.DOTALL)
            if think_match:
                result["think"] = think_match.group(1).strip()
            
            answer_match = re.search(r"<answer>(.*?)</answer>", response, re.DOTALL)
            if answer_match:
                result["answer"] = answer_match.group(1).strip()
            
            # 如果答案仍然包含思考标签，清理它
            if result["answer"]:
                result["answer"] = re.sub(r"<think>.*?</think>", "", result["answer"], flags=re.DOTALL).strip()

            # 如果没有找到answer标签，但找到了think标签，则将响应的其余部分作为答案
            if not result["answer"] and result["think"]:
                remaining_response = response.replace(think_match.group(0), "").strip()
                result["answer"] = re.sub(r"<.*?>", "", remaining_response, flags=re.DOTALL).strip()

            # 如果答案仍然为空，但思考不为空，则使用思考内容作为答案
            if not result["answer"] and result["think"]:
                 result["answer"] = result["think"]
            
            # 如果最终答案和思考都为空，则将原始响应作为答案
            if not result["answer"] and not result["think"]:
                result["answer"] = re.sub(r"<.*?>", "", response, flags=re.DOTALL).strip()
        else:
            result["answer"] = re.sub(r"<.*?>", "", response, flags=re.DOTALL).strip()
            
        return result
    
    def format_assistant_response(self, parsed_response: Dict[str, str]) -> str:
        """格式化助手响应"""
        think_part = parsed_response["think"] if self.enable_think else ""
        answer_part = parsed_response["answer"]
        return f"<think>{think_part}</think>\n<answer>{answer_part}</answer>"
    
    def process_single_image(self, image_path: str) -> List[Dict[str, Any]]:
        """处理单张图片，支持动态QA生成和一对多模式"""
        print(f"处理图片: {os.path.basename(image_path)}")
        image_base64 = self.encode_image_to_base64(image_path)
        if not image_base64:
            return []

        generated_samples = []

        if self.qa_generation_mode:
            qa_pair = self.call_vision_model_for_qa(image_base64)
            if qa_pair and 'question' in qa_pair and 'answer' in qa_pair:
                question = qa_pair['question']
                # 处理包含think字段的JSON响应
                if 'think' in qa_pair and qa_pair['think'] and self.enable_think:
                    # 格式化为<think>...</think>\n<answer>...</answer>格式
                    assistant_content = f"<think>{qa_pair['think']}</think>\n<answer>{qa_pair['answer']}</answer>"
                else:
                    # 如果没有think字段或enable_think为false，使用空的think标签
                    think_content = qa_pair.get('think', '') if self.enable_think else ''
                    assistant_content = f"<think>{think_content}</think>\n<answer>{qa_pair['answer']}</answer>"
                
                relative_image_path = os.path.relpath(image_path, ".").replace('\\', '/')
                sample = {
                    "messages": [
                        {"content": f"<image>{question}", "role": "user"},
                        {"content": assistant_content, "role": "assistant"}
                    ],
                    "images": [relative_image_path]
                }
                generated_samples.append(sample)
        else:
            # --- 旧的问答逻辑 ---
            questions_to_ask = self.questions
            for question in questions_to_ask:
                response = self.call_vision_model(image_base64, question)
                if response:
                    parsed_response = self.parse_response(response)
                    assistant_content = self.format_assistant_response(parsed_response)
                    relative_image_path = os.path.relpath(image_path, ".").replace('\\', '/')
                    
                    sample = {
                        "messages": [
                            {"content": f"<image>{question}", "role": "user"},
                            {"content": assistant_content, "role": "assistant"}
                        ],
                        "images": [relative_image_path]
                    }
                    generated_samples.append(sample)
                time.sleep(0.5)

        return generated_samples
    
    def process_batch(self, image_batch: List[str]) -> List[Dict[str, Any]]:
        """使用并发处理一批图片"""
        batch_results = []
        with concurrent.futures.ThreadPoolExecutor(max_workers=self.batch_size) as executor:
            future_to_image = {executor.submit(self.process_single_image, image_path): image_path for image_path in image_batch}

            for future in concurrent.futures.as_completed(future_to_image):
                image_path = future_to_image[future]
                try:
                    samples_from_image = future.result()
                    if samples_from_image:
                        batch_results.extend(samples_from_image)
                    
                    with self.lock:
                        self.processed_count += 1
                        if samples_from_image:
                            self.success_count += len(samples_from_image)
                        else:
                            self.error_count += 1
                        
                        # 在锁内打印，确保计数准确
                        if self.processed_count % 10 == 0 or self.processed_count == len(self.training_samples):
                            print(f"进度: {self.processed_count}/{len(self.training_samples)} | "
                                  f"成功样本: {self.success_count} | 失败图片: {self.error_count}")

                except Exception as e:
                    with self.lock:
                        self.error_count += 1
                        self.processed_count += 1
                    print(f"处理图片时出现并发错误 {os.path.basename(image_path)}: {e}")
        return batch_results
    
    def save_results_to_jsonl(self, samples: List[Dict[str, Any]]):
        """将一批样本追加到JSONL文件"""
        if not self.output_filename:
            timestamp = time.strftime("%Y%m%d_%H%M%S")
            filename = f"{self.subject_name}_training_data_{timestamp}.jsonl"
            self.output_filename = os.path.join(self.output_folder, filename)
            # 首次创建文件时清空
            with open(self.output_filename, 'w', encoding='utf-8') as f:
                pass
            print(f"输出文件已创建: {self.output_filename}")

        with open(self.output_filename, 'a', encoding='utf-8') as f:
            for sample in samples:
                f.write(json.dumps(sample, ensure_ascii=False) + '\n')
        
        print(f"已将 {len(samples)} 个新样本追加到 {os.path.basename(self.output_filename)}")
    
    def run(self):
        """运行训练集生成"""
        print("=== 训练集生成工具 ===")
        if self.qa_generation_mode:
            mode = "动态QA生成"
            print(f"模式: {mode}, 主题: {self.subject_name}")
        else:
            mode = "一对多" if self.multi_question_mode else "一对一"
            print(f"模式: {mode}, 主题: {self.subject_name}, 思考模式: {'启用' if self.enable_think else '禁用'}")

        print(f"并发数: {self.batch_size}, 总图片数: {self.total_count}")
        print(f"模型: {self.model_name}, API地址: {self.client.base_url}\n")
        
        all_image_files = self.scan_images()
        if not all_image_files:
            print("没有找到图片文件！")
            return
        
        total_to_process = len(all_image_files) if self.total_count == "auto" else min(int(self.total_count), len(all_image_files))
        print(f"将处理 {total_to_process} 张图片")
        
        random.shuffle(all_image_files)
        images_to_process = all_image_files[:total_to_process]
        self.training_samples = images_to_process # 用于进度报告
        
        # 确保首次调用save_results_to_jsonl时能创建文件
        if images_to_process:
             self.save_results_to_jsonl([])

        total_batches = (total_to_process + self.batch_size - 1) // self.batch_size
        
        for batch_idx in range(total_batches):
            start_idx = batch_idx * self.batch_size
            end_idx = min(start_idx + self.batch_size, total_to_process)
            current_batch_size = end_idx - start_idx
            
            print(f"\n处理第 {batch_idx + 1}/{total_batches} 批 (大小: {current_batch_size})")
            
            batch_images = images_to_process[start_idx:end_idx]
            batch_results = self.process_batch(batch_images)
            
            if batch_results:
                self.save_results_to_jsonl(batch_results)
        
        print(f"\n=== 处理完成 ===\n总处理图片: {self.processed_count}, 成功生成样本: {self.success_count}, 失败图片: {self.error_count}")
        print(f"所有数据已保存至: {self.output_filename}")

def main():
    """主函数"""
    import sys
    config_file = "training_config.json" if len(sys.argv) == 1 else sys.argv[1]
    config = load_config(config_file)
    generator = TrainingDatasetGenerator(config)
    generator.run()

if __name__ == "__main__":
    main()