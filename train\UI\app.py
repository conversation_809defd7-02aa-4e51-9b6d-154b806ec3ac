import os
import sys
import json
import time
import plotly.graph_objects as go
import pandas as pd
import numpy as np
import gradio as gr
import subprocess
import threading
import argparse
from queue import Queue
import matplotlib.pyplot as plt
from datetime import datetime
import torch
import psutil
import GPUtil
import signal
import traceback

# 导入当前目录下的训练脚本
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.append(current_dir)

# 导入模型配置
try:
    from model_config import (
        get_model_path, get_model_names, get_default_model,
        load_model_config, add_or_update_model, delete_model,
        set_default_model
    )
    # 加载模型配置
    MODEL_PATHS, DEFAULT_MODEL = load_model_config()
except ImportError as e:
    print(f"警告：无法导入model_config模块: {e}")
    print(traceback.format_exc())
    # 使用默认配置
    MODEL_PATHS = {
        "Qwen2.5-1.5B-Instruct": "/ssd1/align/Qwen2.5-1.5B-Instruct",
        "Qwen2.5-7B-Instruct": "/ssd1/align/Qwen2.5-7B-Instruct",
        "DeepSeek-R1-Distill-Qwen-1.5B": "/ssd2/llm/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"
    }
    DEFAULT_MODEL = "Qwen2.5-1.5B-Instruct"

# 导入训练相关模块
sft_train = None
rl_train = None
dpo_train = None
try:
    from train_sft import train as sft_train
except ImportError as e:
    print(f"警告：无法导入SFT训练模块: {e}")
    print(f"请确保安装了以下依赖: torch, transformers, unsloth, trl, datasets")
    print(traceback.format_exc())

try:
    from train_rl import train as rl_train
except ImportError as e:
    print(f"警告：无法导入RL训练模块: {e}")
    print(f"请确保安装了以下依赖: torch, transformers, trl, vllm, sentence-transformers")
    print(traceback.format_exc())
except FileNotFoundError as e:
    print(f"警告：导入RL训练模块时找不到数据文件: {e}")
    print(f"这不会影响UI的启动，但在开始训练前请确保数据文件存在")
    print(traceback.format_exc())

try:
    from train_dpo import train as dpo_train
except ImportError as e:
    # DPO可能不完全支持
    print(f"警告：无法导入DPO训练模块: {e}")
    print(traceback.format_exc())
except FileNotFoundError as e:
    print(f"警告：导入DPO训练模块时找不到数据文件: {e}")
    print(traceback.format_exc())

# 全局变量
TRAINING_PROCESS = None
STOP_EVENT = threading.Event()
METRICS_QUEUE = Queue()
CURRENT_LOG = []
MODELS = MODEL_PATHS
TARGET_MODULES = ["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]

# 存储已加载的模型
LOADED_MODEL = None
LOADED_TOKENIZER = None
LOADED_MODEL_PATH = None
LOADED_LORA_PATH = None

# 创建输出目录
os.makedirs(os.path.join(current_dir, "outputs"), exist_ok=True)

# 创建数据目录
os.makedirs(os.path.join(current_dir, "data"), exist_ok=True)

# 导入数据泛化模块
try:
    from data_augmentation import DataAugmentation, create_augmentation_ui
except ImportError as e:
    print(f"警告：无法导入数据泛化模块: {e}")
    print(traceback.format_exc())

# 检查训练脚本是否存在
def check_training_files():
    """检查训练文件是否存在且可读"""
    result = {}
    
    # 检查SFT训练脚本
    sft_path = os.path.join(current_dir, "train_sft.py")
    if os.path.exists(sft_path):
        if os.access(sft_path, os.R_OK):
            result["sft"] = "可用"
        else:
            result["sft"] = "文件存在但无法读取，请检查权限"
    else:
        result["sft"] = "文件不存在"
    
    # 检查RL训练脚本
    rl_path = os.path.join(current_dir, "train_rl.py")
    if os.path.exists(rl_path):
        if os.access(rl_path, os.R_OK):
            result["rl"] = "可用"
        else:
            result["rl"] = "文件存在但无法读取，请检查权限"
    else:
        result["rl"] = "文件不存在"
    
    # 检查数据文件
    data_path = os.path.join(current_dir, "data", "data.json")
    if os.path.exists(data_path):
        if os.access(data_path, os.R_OK):
            result["data"] = "可用"
        else:
            result["data"] = "文件存在但无法读取，请检查权限"
    else:
        result["data"] = "文件不存在"
    
    print("\n========== 训练文件检查 ==========")
    for k, v in result.items():
        print(f"{k}: {v}")
    
    return result

# 启动时检查训练文件
check_training_files()

# 工具函数
def load_jsonl(file_path):
    """加载JSONL文件"""
    data = []
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            for line in f:
                data.append(json.loads(line))
        return data
    except Exception as e:
        print(f"加载JSONL文件失败: {e}")
        return []

def load_json(file_path):
    """加载JSON文件"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            return json.load(f)
    except Exception as e:
        print(f"加载JSON文件失败: {e}")
        return []

def save_json(data, file_path):
    """保存JSON文件"""
    try:
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存JSON文件失败: {e}")
        return False

def get_gpu_info():
    """获取GPU信息"""
    gpus = GPUtil.getGPUs()
    if not gpus:
        return {"利用率": 0, "显存": 0, "温度": 0}
    
    gpu = gpus[0]  # 使用第一个GPU
    return {
        "利用率": round(gpu.load * 100, 2),
        "显存": round(gpu.memoryUsed / gpu.memoryTotal * 100, 2),
        "温度": round(gpu.temperature, 1)
    }

def monitor_training_process():
    """监控训练进程并更新UI"""
    global CURRENT_LOG
    
    print(f"训练监控线程已启动")
    while not STOP_EVENT.is_set():
        try:
            # 从队列获取最新指标
            if not METRICS_QUEUE.empty():
                metrics = METRICS_QUEUE.get()
                
                # 添加时间戳
                metrics["timestamp"] = time.time()
                metrics["time"] = time.strftime("%H:%M:%S")
                
                # 检查是否有错误信息
                if "error" in metrics:
                    error_msg = metrics["error"]
                    print(f"训练错误: {error_msg}")
                    # 添加错误信息到日志
                    CURRENT_LOG.append({
                        "step": len(CURRENT_LOG) + 1,
                        "timestamp": time.time(),
                        "time": time.strftime("%H:%M:%S"),
                        "loss": 0,
                        "learning_rate": 0,
                        "step_time": 0,
                        "gpu_util": 0,
                        "gpu_mem": 0,
                        "status": f"错误: {error_msg}"
                    })
                else:
                    # 正常指标
                    print(f"训练指标更新: {metrics}")
                    CURRENT_LOG.append(metrics)
                
            time.sleep(0.5)  # 更快地检查队列
        except Exception as e:
            print(f"监控训练进程出错: {e}")
            print(traceback.format_exc())
            time.sleep(5)

def stop_training():
    """停止训练进程"""
    global TRAINING_PROCESS, STOP_EVENT
    
    if TRAINING_PROCESS is not None:
        STOP_EVENT.set()
        try:
            if hasattr(TRAINING_PROCESS, 'terminate'):
                TRAINING_PROCESS.terminate()
            elif isinstance(TRAINING_PROCESS, subprocess.Popen):
                TRAINING_PROCESS.send_signal(signal.SIGTERM)
            
            TRAINING_PROCESS = None
            return "训练已停止"
        except Exception as e:
            return f"停止训练失败: {e}"
    return "没有正在运行的训练"

def create_loss_chart(log_data):
    """创建损失曲线图"""
    if not log_data:
        return go.Figure()
    
    steps = [entry.get("step", i) for i, entry in enumerate(log_data)]
    losses = [entry.get("loss", 0) for entry in log_data]
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=steps, y=losses, mode='lines', name='训练损失'))
    fig.update_layout(
        title="训练损失曲线",
        xaxis_title="步数",
        yaxis_title="损失值",
        template="plotly_white"
    )
    return fig

def create_lr_chart(log_data):
    """创建学习率曲线图"""
    if not log_data:
        return go.Figure()
    
    steps = [entry.get("step", i) for i, entry in enumerate(log_data)]
    lrs = [entry.get("learning_rate", 0) for entry in log_data]
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=steps, y=lrs, mode='lines', name='学习率'))
    fig.update_layout(
        title="学习率变化曲线",
        xaxis_title="步数",
        yaxis_title="学习率",
        template="plotly_white"
    )
    return fig

def create_gpu_chart(log_data):
    """创建GPU使用率图表"""
    if not log_data:
        return go.Figure()
    
    steps = [entry.get("step", i) for i, entry in enumerate(log_data)]
    gpu_utils = [entry.get("gpu_util", 0) for entry in log_data]
    gpu_mems = [entry.get("gpu_mem", 0) for entry in log_data]
    
    fig = go.Figure()
    fig.add_trace(go.Scatter(x=steps, y=gpu_utils, mode='lines', name='GPU利用率'))
    fig.add_trace(go.Scatter(x=steps, y=gpu_mems, mode='lines', name='显存使用率'))
    fig.update_layout(
        title="GPU资源使用情况",
        xaxis_title="步数",
        yaxis_title="使用率(%)",
        template="plotly_white"
    )
    return fig

def parse_learning_rate(lr_str):
    """将科学计数法字符串转换为浮点数"""
    try:
        # 尝试直接转换（处理 "1e-4" 这样的格式）
        return float(lr_str)
    except ValueError:
        try:
            # 处理可能的空格（如 "1 e-4"）
            lr_str = lr_str.replace(" ", "")
            return float(lr_str)
        except ValueError:
            # 如果转换失败，返回默认值
            print(f"警告：无法解析学习率 '{lr_str}'，使用默认值 2e-4")
            return 2e-4

def start_sft_training(model_path, train_mode, system_message, lora_rank, lora_alpha, 
                      batch_size, learning_rate, num_epochs, gradient_accumulation, 
                      selected_modules, output_dir):
    """启动SFT训练"""
    global TRAINING_PROCESS, STOP_EVENT, METRICS_QUEUE, CURRENT_LOG
    
    STOP_EVENT.clear()
    CURRENT_LOG = []
    
    # 从显示名称中提取实际的模型名称
    if " (" in model_path and ")" in model_path:
        model_name = model_path.split(" (")[0]
    else:
        model_name = model_path
    
    # 打印详细参数信息
    print(f"\n========== 开始SFT训练 ==========")
    print(f"模型名称: {model_name}")
    print(f"训练模式: {train_mode}")
    print(f"LoRA参数: rank={lora_rank}, alpha={lora_alpha}")
    print(f"批次大小: {batch_size}")
    print(f"学习率: {learning_rate}")
    print(f"训练轮数: {num_epochs}")
    print(f"梯度累积: {gradient_accumulation}")
    print(f"目标模块: {selected_modules}")
    print(f"输出目录: {output_dir}")
    
    # 构建参数
    args = argparse.Namespace(
        model_path=model_name,  # 使用提取的模型名称
        train_mode=train_mode,
        system_message=system_message,
        lora_rank=lora_rank,
        lora_alpha=lora_alpha,
        batch_size=batch_size,
        learning_rate=learning_rate,
        num_epochs=num_epochs,
        gradient_accumulation=gradient_accumulation,
        target_modules=selected_modules,
        output_dir=output_dir,
        max_seq_length=2048  # 添加最大序列长度参数
    )
    
    # 启动训练线程
    def training_thread():
        try:
            print(f"训练线程已启动，正在初始化训练环境...")
            # 检查训练函数是否存在
            if sft_train is None:
                error_msg = "错误：SFT训练模块未正确加载，请确保train_sft.py可用并且依赖已安装"
                print(error_msg)
                METRICS_QUEUE.put({"error": error_msg})
                return
                
            # 确保输出目录存在
            os.makedirs(args.output_dir, exist_ok=True)
            
            # 调用训练函数
            print(f"开始调用SFT训练函数...")
            sft_train(args, metrics_queue=METRICS_QUEUE, stop_event=STOP_EVENT)
            print(f"SFT训练函数已完成")
            
        except Exception as e:
            error_msg = f"SFT训练出错: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            METRICS_QUEUE.put({"error": error_msg})
    
    # 创建训练线程
    thread = threading.Thread(target=training_thread)
    thread.daemon = True
    
    try:
        # 启动线程
        print(f"正在启动训练线程...")
        thread.start()
        
        TRAINING_PROCESS = thread
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_training_process)
        monitor_thread.daemon = True
        monitor_thread.start()
        print(f"监控线程已启动，可在监控面板查看进度")
        
        return "SFT训练已启动，请在监控面板查看进度"
    except Exception as e:
        error_msg = f"启动训练线程失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return error_msg

def start_rl_training(model_path, train_mode, lora_rank, lora_alpha, 
                     batch_size, learning_rate, max_steps, beta,
                     selected_modules, output_dir, format_reward_weight,
                     semantic_reward_weight, reasoning_reward_weight,
                     gpu_memory_utilization):
    """启动RL训练"""
    global TRAINING_PROCESS, STOP_EVENT, METRICS_QUEUE, CURRENT_LOG
    
    STOP_EVENT.clear()
    CURRENT_LOG = []
    
    # 打印详细参数信息
    print(f"\n========== 开始RL训练 ==========")
    print(f"模型路径: {model_path}")
    print(f"训练模式: {train_mode}")
    print(f"LoRA参数: rank={lora_rank}, alpha={lora_alpha}")
    print(f"批次大小: {batch_size}")
    print(f"学习率: {learning_rate}")
    print(f"最大步数: {max_steps}")
    print(f"Beta值: {beta}")
    print(f"目标模块: {selected_modules}")
    print(f"输出目录: {output_dir}")
    print(f"奖励权重: 格式={format_reward_weight}, 语义={semantic_reward_weight}, 推理={reasoning_reward_weight}")
    print(f"GPU显存利用率: {gpu_memory_utilization}")
    
    # 构建参数
    args = argparse.Namespace(
        model_path=model_path,
        train_mode=train_mode,
        lora_rank=lora_rank,
        lora_alpha=lora_alpha,
        batch_size=batch_size,
        learning_rate=learning_rate,
        max_steps=max_steps,
        beta=beta,
        target_modules=selected_modules,
        output_dir=output_dir,
        format_reward_weight=format_reward_weight,
        semantic_reward_weight=semantic_reward_weight,
        reasoning_reward_weight=reasoning_reward_weight,
        gpu_memory_utilization=gpu_memory_utilization
    )
    
    # 启动训练线程
    def training_thread():
        try:
            print(f"RL训练线程已启动，正在初始化训练环境...")
            # 检查训练函数是否存在
            if rl_train is None:
                error_msg = "错误：RL训练模块未正确加载，请确保train_rl.py可用并且依赖已安装"
                print(error_msg)
                METRICS_QUEUE.put({"error": error_msg})
                return
                
            # 检查数据文件是否存在
            data_path = os.path.join(current_dir, "data", "data.json")
            if not os.path.exists(data_path):
                error_msg = f"错误：找不到数据文件 {data_path}，请确保上传并处理了训练数据"
                print(error_msg)
                METRICS_QUEUE.put({"error": error_msg})
                return
                
            # 确保输出目录存在
            os.makedirs(args.output_dir, exist_ok=True)
            
            # 调用训练函数
            print(f"开始调用RL训练函数...")
            rl_train(args, metrics_queue=METRICS_QUEUE, stop_event=STOP_EVENT)
            print(f"RL训练函数已完成")
            
        except Exception as e:
            error_msg = f"RL训练出错: {str(e)}"
            print(error_msg)
            print(traceback.format_exc())
            METRICS_QUEUE.put({"error": error_msg})
    
    # 创建训练线程
    thread = threading.Thread(target=training_thread)
    thread.daemon = True
    
    try:
        # 启动线程
        print(f"正在启动RL训练线程...")
        thread.start()
        
        TRAINING_PROCESS = thread
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=monitor_training_process)
        monitor_thread.daemon = True
        monitor_thread.start()
        print(f"监控线程已启动，可在监控面板查看进度")
        
        return "RL训练已启动，请在监控面板查看进度"
    except Exception as e:
        error_msg = f"启动RL训练线程失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return error_msg

def run_evaluation_with_loaded_model(question, max_length):
    """使用已加载的模型进行评估"""
    global LOADED_MODEL, LOADED_TOKENIZER
    
    if LOADED_MODEL is None or LOADED_TOKENIZER is None:
        return "请先加载模型"
    
    try:
        # 导入eval.py中的函数
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from eval import eval_with_model
        
        # 调用eval_with_model函数
        output = eval_with_model(
            model=LOADED_MODEL,
            tokenizer=LOADED_TOKENIZER,
            question=question,
            max_tokens=max_length
        )
        
        return output
        
    except Exception as e:
        error_msg = f"评估失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return error_msg

def run_evaluation(model_path, custom_path, lora_dir, question, max_length):
    """运行模型评估 - 考虑自定义模型路径"""
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    # 确定实际使用的模型路径
    actual_model_path = custom_path if custom_path and custom_path.strip() else None
    if not actual_model_path:
        # 从显示名称中提取实际的模型名称和路径
        if " (" in model_path and ")" in model_path:
            model_name = model_path.split(" (")[0]
            if model_name in MODELS:
                actual_model_path = MODELS[model_name]
            else:
                actual_model_path = model_path
        else:
            if model_path in MODELS:
                actual_model_path = MODELS[model_path]
            else:
                actual_model_path = model_path
    
    # 如果已加载模型并且参数匹配，使用已加载的模型
    if (LOADED_MODEL is not None and 
        LOADED_MODEL_PATH == actual_model_path and
        LOADED_LORA_PATH == lora_dir):
        
        return run_evaluation_with_loaded_model(question, max_length)
    
    # 否则按原逻辑执行
    try:
        import argparse
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from eval import eval
        
        args = argparse.Namespace(
            model_path=actual_model_path,
            lora_dir=lora_dir,
            question=question,
            max_seq_length=max_length,
            max_tokens=max_length,
            lora_rank=8,
            train_mode="origin",
            type="rl",
            system_message="instruction",
            lora_alpha=16,
            use_loaded_model=False
        )
        
        output = eval(args)
        return output
    except Exception as e:
        return f"评估失败: {e}"

def load_model(model_path, custom_path=None, lora_dir=None, max_seq_length=2048):
    """加载模型和tokenizer，可选地加载LoRA权重"""
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    try:
        if LOADED_MODEL is not None:
            return f"模型已加载: {LOADED_MODEL_PATH}，请先卸载当前模型"
        
        # 如果提供了自定义路径，优先使用
        actual_model_path = custom_path if custom_path and custom_path.strip() else None
        
        # 如果没有自定义路径，使用下拉菜单选择的模型
        if not actual_model_path:
            # 从显示名称中提取实际的模型名称和路径
            if " (" in model_path and ")" in model_path:
                model_name = model_path.split(" (")[0]
                if model_name in MODELS:
                    actual_model_path = MODELS[model_name]
                else:
                    actual_model_path = model_path
            else:
                if model_path in MODELS:
                    actual_model_path = MODELS[model_path]
                else:
                    actual_model_path = model_path
            
        print(f"正在加载模型: {actual_model_path}")
        
        # 处理LoRA路径
        if lora_dir and lora_dir.strip():
            lora_dir = lora_dir.strip()
            # 检查是否为绝对路径
            if not os.path.isabs(lora_dir):
                # 如果是相对路径，转换为绝对路径
                lora_dir = os.path.abspath(lora_dir)
            print(f"使用LoRA路径: {lora_dir}")
            
            # 检查adapter_config.json是否存在
            adapter_config_path = os.path.join(lora_dir, "adapter_config.json")
            if not os.path.exists(adapter_config_path):
                print(f"警告: 找不到adapter_config.json文件: {adapter_config_path}")
                # 尝试在lora_model_final目录下查找
                alternative_path = os.path.join(lora_dir, "lora_model_final", "adapter_config.json")
                if os.path.exists(alternative_path):
                    print(f"找到替代的adapter_config.json文件: {alternative_path}")
                    lora_dir = os.path.join(lora_dir, "lora_model_final")
        
        # 导入eval.py中的函数
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from eval import load_model_for_eval
        
        # 使用load_model_for_eval函数加载模型
        model, tokenizer = load_model_for_eval(
            model_path=actual_model_path,
            lora_dir=lora_dir,
            max_seq_length=max_seq_length
        )
        
        if model is None or tokenizer is None:
            return f"加载模型失败: {actual_model_path}"
        
        # 更新全局变量
        LOADED_MODEL = model
        LOADED_TOKENIZER = tokenizer
        LOADED_MODEL_PATH = actual_model_path
        LOADED_LORA_PATH = lora_dir if lora_dir and lora_dir.strip() else None
        
        return f"成功加载模型: {actual_model_path}" + (f"\n已加载LoRA权重: {lora_dir}" if lora_dir and lora_dir.strip() else "")
        
    except Exception as e:
        error_msg = f"加载模型失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return error_msg

def unload_model():
    """卸载已加载的模型"""
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    if LOADED_MODEL is None:
        return "没有已加载的模型"
    
    try:
        # 导入eval.py中的函数
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from eval import unload_model_for_eval
        
        # 调用unload_model_for_eval函数
        result = unload_model_for_eval()
        
        # 重置全局变量 (虽然unload_model_for_eval已经重置了全局变量，但为了安全起见再次重置)
        LOADED_MODEL = None
        LOADED_TOKENIZER = None
        LOADED_MODEL_PATH = None
        LOADED_LORA_PATH = None
        
        return result
        
    except Exception as e:
        error_msg = f"卸载模型失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return error_msg

def run_evaluation_with_loaded_model(question, max_length):
    """使用已加载的模型进行评估"""
    global LOADED_MODEL, LOADED_TOKENIZER
    
    if LOADED_MODEL is None or LOADED_TOKENIZER is None:
        return "请先加载模型"
    
    try:
        # 导入eval.py中的函数
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from eval import eval_with_model
        
        # 调用eval_with_model函数
        output = eval_with_model(
            model=LOADED_MODEL,
            tokenizer=LOADED_TOKENIZER,
            question=question,
            max_tokens=max_length
        )
        
        return output
        
    except Exception as e:
        error_msg = f"评估失败: {str(e)}"
        print(error_msg)
        print(traceback.format_exc())
        return error_msg

def run_evaluation(model_path, custom_path, lora_dir, question, max_length):
    """运行模型评估 - 考虑自定义模型路径"""
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    # 确定实际使用的模型路径
    actual_model_path = custom_path if custom_path and custom_path.strip() else None
    if not actual_model_path:
        # 从显示名称中提取实际的模型名称和路径
        if " (" in model_path and ")" in model_path:
            model_name = model_path.split(" (")[0]
            if model_name in MODELS:
                actual_model_path = MODELS[model_name]
            else:
                actual_model_path = model_path
        else:
            if model_path in MODELS:
                actual_model_path = MODELS[model_path]
            else:
                actual_model_path = model_path
    
    # 如果已加载模型并且参数匹配，使用已加载的模型
    if (LOADED_MODEL is not None and 
        LOADED_MODEL_PATH == actual_model_path and
        LOADED_LORA_PATH == lora_dir):
        
        return run_evaluation_with_loaded_model(question, max_length)
    
    # 否则按原逻辑执行
    try:
        import argparse
        import sys
        sys.path.append(os.path.dirname(os.path.abspath(__file__)))
        from eval import eval
        
        args = argparse.Namespace(
            model_path=actual_model_path,
            lora_dir=lora_dir,
            question=question,
            max_seq_length=max_length,
            max_tokens=max_length,
            lora_rank=8,
            train_mode="origin",
            type="rl",
            system_message="instruction",
            lora_alpha=16,
            use_loaded_model=False
        )
        
        output = eval(args)
        return output
    except Exception as e:
        return f"评估失败: {e}"

# 更新数据处理函数 - 确保此函数在build_ui之前定义
def process_uploaded_data(input_type, files, data_path, data_format, 
                         question_field, answer_field, cot_field, background_field,
                         cot_format, split_ratio, output_format, expected_count, timeout, max_samples):
    """处理上传的数据文件或本地路径指定的数据文件"""
    try:
        # 导入cot模块中的函数
        try:
            from cot import map_jsonl_fields, read_json, read_background_knowledge, write_json, write_jsonl, check_output_file
        except ImportError as e:
            print(f"警告：导入cot模块时出错: {e}，将使用内置函数")
            # 如果无法导入，使用内置的简化版函数
            def map_jsonl_fields(data, field_mapping):
                mapped_data = []
                for item in data:
                    mapped_item = {}
                    for target_field, source_field in field_mapping.items():
                        if source_field in item:
                            mapped_item[target_field] = item[source_field]
                    if "question" in mapped_item:  # 确保至少有问题字段
                        mapped_data.append(mapped_item)
                return mapped_data
                
            def read_json(file_path):
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
            def write_json(data, file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(data, f, ensure_ascii=False, indent=2)
                return True
                
            def write_jsonl(data, file_path):
                with open(file_path, 'w', encoding='utf-8') as f:
                    for item in data:
                        f.write(json.dumps(item, ensure_ascii=False) + '\n')
                return True
                
            def read_background_knowledge(file_path):
                if not os.path.exists(file_path):
                    return ""
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
                    
            def check_output_file(file_path, file_type, expected_count=None):
                if os.path.exists(file_path):
                    return True, 0
                return False, 0
        
        # 创建字段映射
        field_mapping = {
            question_field: "question",
            answer_field: "answer"
        }
        
        # 添加可选字段
        if cot_field and cot_field.strip():
            field_mapping[cot_field] = "cot"
        
        if background_field and background_field.strip():
            field_mapping[background_field] = "background"
        
        # 加载数据
        data = []
        
        if input_type == "上传文件" and files:
            for file in files:
                file_path = file.name
                file_ext = os.path.splitext(file_path)[1].lower()
                
                if file_ext in ['.json']:
                    try:
                        loaded_data = read_json(file_path)
                        if isinstance(loaded_data, list):
                            data.extend(loaded_data)
                        else:
                            data.append(loaded_data)
                    except Exception as e:
                        return {"output": f"无法解析JSON文件: {file_path}, 错误: {str(e)}", "preview": []}
                
                elif file_ext in ['.jsonl']:
                    try:
                        with open(file_path, 'r', encoding='utf-8') as f:
                            for line in f:
                                if line.strip():
                                    data.append(json.loads(line))
                    except Exception as e:
                        return {"output": f"无法解析JSONL文件: {file_path}, 错误: {str(e)}", "preview": []}
                
                elif file_ext in ['.xlsx', '.xls']:
                    try:
                        df = pd.read_excel(file_path)
                        data.extend(df.to_dict('records'))
                    except Exception as e:
                        return {"output": f"无法解析Excel文件: {file_path}, 错误: {str(e)}", "preview": []}
                
                else:
                    return {"output": f"不支持的文件格式: {file_ext}", "preview": []}
        
        elif input_type == "本地路径" and data_path:
            if not os.path.exists(data_path):
                return {"output": f"文件不存在: {data_path}", "preview": []}
            
            if data_format == "json":
                try:
                    loaded_data = read_json(data_path)
                    if isinstance(loaded_data, list):
                        data.extend(loaded_data)
                    else:
                        data.append(loaded_data)
                except Exception as e:
                    return {"output": f"无法解析JSON文件: {data_path}, 错误: {str(e)}", "preview": []}
            
            elif data_format == "jsonl":
                try:
                    with open(data_path, 'r', encoding='utf-8') as f:
                        for line in f:
                            if line.strip():
                                data.append(json.loads(line))
                except Exception as e:
                    return {"output": f"无法解析JSONL文件: {data_path}, 错误: {str(e)}", "preview": []}
            
            elif data_format == "excel":
                try:
                    df = pd.read_excel(data_path)
                    data.extend(df.to_dict('records'))
                except Exception as e:
                    return {"output": f"无法解析Excel文件: {data_path}, 错误: {str(e)}", "preview": []}
        
        else:
            return {"output": "请上传文件或提供本地文件路径", "preview": []}
        
        # 限制处理的样本数
        if max_samples > 0 and len(data) > max_samples:
            data = data[:max_samples]
            print(f"已限制处理样本数为: {max_samples}")
        
        # 应用字段映射
        mapped_data = map_jsonl_fields(data, field_mapping)
        
        # 处理背景知识
        if "background" in field_mapping.values():
            for item in mapped_data:
                if "background" in item and item["background"]:
                    try:
                        # 如果背景知识是文件路径，尝试读取
                        if isinstance(item["background"], str) and os.path.exists(item["background"]):
                            item["background"] = read_background_knowledge(item["background"])
                    except Exception as e:
                        print(f"处理背景知识时出错: {str(e)}")
        
        # 分割训练集和验证集
        total_count = len(mapped_data)
        train_count = int(total_count * split_ratio)
        
        # 随机打乱数据
        import random
        random.shuffle(mapped_data)
        
        train_data = mapped_data[:train_count]
        val_data = mapped_data[train_count:]
        
        # 保存处理后的数据
        os.makedirs("data", exist_ok=True)
        
        if output_format == "json":
            train_output = os.path.join("data", "train_data.json")
            val_output = os.path.join("data", "val_data.json")
            write_json(train_data, train_output)
            write_json(val_data, val_output)
        else:  # jsonl
            train_output = os.path.join("data", "train_data.jsonl")
            val_output = os.path.join("data", "val_data.jsonl")
            write_jsonl(train_data, train_output)
            write_jsonl(val_data, val_output)
        
        # 合并数据用于训练
        combined_output = os.path.join("data", "data.json")
        write_json(mapped_data, combined_output)
        
        # 检查输出文件
        check_result, _ = check_output_file(combined_output, "json", expected_count if expected_count > 0 else None)
        
        # 准备预览数据
        preview_data = []
        for item in mapped_data[:10]:  # 只显示前10条
            preview_data.append([
                item.get("question", ""),
                item.get("answer", ""),
                item.get("cot", ""),
                item.get("background", "")
            ])
        
        return {
            "output": f"已成功处理 {total_count} 条数据，分割为 {train_count} 条训练数据和 {total_count - train_count} 条验证数据。\n"
                    f"训练数据保存至: {train_output}\n"
                    f"验证数据保存至: {val_output}\n"
                    f"合并数据保存至: {combined_output}\n"
                    f"检查结果: {'成功' if check_result else '失败'}",
            "preview": preview_data
        }
    
    except Exception as e:
        import traceback
        error_msg = f"处理数据时出错: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return {"output": error_msg, "preview": []}

# 数据管理面板
def build_ui():
    with gr.Blocks() as demo:
        gr.Markdown("# 智能对话训练平台")
        
        with gr.Tabs() as tabs:
            # 模型选择与配置面板
            with gr.Tab("模型选择与配置"):
                with gr.Row():
                    with gr.Column(scale=1):
                        model_type = gr.Dropdown(
                            choices=["Qwen2.5-1.5B-Instruct", "Qwen2.5-7B-Instruct", "DeepSeek-R1-Distill-Qwen-1.5B"],
                            label="选择基础模型",
                            value="Qwen2.5-1.5B-Instruct"
                        )
                        
                        train_mode = gr.Radio(
                            choices=["origin", "distill"],
                            label="训练模式",
                            value="origin"
                        )
                        
                        system_prompt = gr.TextArea(
                            label="系统提示词",
                            value="You are a helpful assistant.",
                            lines=3
                        )
                        
                    with gr.Column(scale=1):
                        lora_rank = gr.Slider(
                            minimum=4, maximum=128, step=4,
                            label="LoRA Rank值",
                            value=8
                        )
                        
                        lora_alpha = gr.Slider(
                            minimum=1, maximum=32, step=1,
                            label="LoRA Alpha值",
                            value=16
                        )
                        
                        target_modules = gr.CheckboxGroup(
                            choices=TARGET_MODULES,
                            label="目标模块",
                            value=TARGET_MODULES
                        )
                
                # 添加模型路径配置部分
                gr.Markdown("## 模型路径配置")
                with gr.Row():
                    with gr.Column(scale=2):
                        # 显示当前配置的模型路径
                        model_paths_table = gr.Dataframe(
                            headers=["模型名称", "模型路径", "是否默认"],
                            label="当前模型配置",
                            interactive=False
                        )
                    
                    with gr.Column(scale=1):
                        # 添加/编辑模型表单
                        gr.Markdown("### 添加或编辑模型")
                        model_name_input = gr.Textbox(
                            label="模型名称",
                            placeholder="例如: Qwen2.5-1.5B-Instruct"
                        )
                        model_path_input = gr.Textbox(
                            label="模型路径",
                            placeholder="例如: /path/to/model"
                        )
                        with gr.Row():
                            add_model_btn = gr.Button("添加/更新模型", variant="primary")
                            delete_model_btn = gr.Button("删除模型", variant="stop")
                            set_default_btn = gr.Button("设为默认", variant="secondary")
                        
                        model_config_status = gr.Textbox(
                            label="操作状态",
                            value="",
                            lines=2
                        )
                        
                        # 刷新按钮
                        refresh_models_btn = gr.Button("刷新模型列表", variant="secondary")
                
                # 添加模型路径配置相关函数
                def load_model_paths():
                    """加载模型路径配置并显示在表格中"""
                    try:
                        # 导入模型配置
                        try:
                            from model_config import load_model_config, get_default_model
                            model_paths, default_model = load_model_config()
                        except ImportError:
                            # 如果导入失败，使用全局变量
                            model_paths = MODELS
                            default_model = DEFAULT_MODEL
                        
                        # 准备表格数据
                        table_data = []
                        for name, path in model_paths.items():
                            is_default = "✓" if name == default_model else ""
                            table_data.append([name, path, is_default])
                        
                        # 更新下拉菜单选项
                        return table_data, gr.update(choices=list(model_paths.keys()), value=default_model)
                    except Exception as e:
                        print(f"加载模型配置失败: {e}")
                        print(traceback.format_exc())
                        return [], gr.update()
                
                def add_or_update_model(name, path):
                    """添加或更新模型配置"""
                    if not name or not name.strip():
                        return "错误: 模型名称不能为空", None, None
                    
                    if not path or not path.strip():
                        return "错误: 模型路径不能为空", None, None
                    
                    try:
                        from model_config import add_or_update_model as add_model_func
                        success = add_model_func(name.strip(), path.strip())
                        if success:
                            table_data, dropdown_update = load_model_paths()
                            return f"成功添加/更新模型: {name}", table_data, dropdown_update
                        else:
                            return "添加/更新模型失败", None, None
                    except Exception as e:
                        return f"添加/更新模型出错: {str(e)}", None, None
                
                def delete_model(name):
                    """删除模型配置"""
                    if not name or not name.strip():
                        return "错误: 模型名称不能为空", None, None
                    
                    try:
                        from model_config import delete_model as delete_model_func
                        success = delete_model_func(name.strip())
                        if success:
                            table_data, dropdown_update = load_model_paths()
                            return f"成功删除模型: {name}", table_data, dropdown_update
                        else:
                            return "删除模型失败", None, None
                    except Exception as e:
                        return f"删除模型出错: {str(e)}", None, None
                
                def set_default_model(name):
                    """设置默认模型"""
                    if not name or not name.strip():
                        return "错误: 模型名称不能为空", None, None
                    
                    try:
                        from model_config import set_default_model as set_default_func
                        success = set_default_func(name.strip())
                        if success:
                            table_data, dropdown_update = load_model_paths()
                            return f"成功设置默认模型: {name}", table_data, dropdown_update
                        else:
                            return "设置默认模型失败", None, None
                    except Exception as e:
                        return f"设置默认模型出错: {str(e)}", None, None
                
                # 注册事件处理函数
                refresh_models_btn.click(
                    fn=load_model_paths,
                    inputs=[],
                    outputs=[model_paths_table, model_type]
                )
                
                add_model_btn.click(
                    fn=add_or_update_model,
                    inputs=[model_name_input, model_path_input],
                    outputs=[model_config_status, model_paths_table, model_type]
                )
                
                delete_model_btn.click(
                    fn=delete_model,
                    inputs=[model_name_input],
                    outputs=[model_config_status, model_paths_table, model_type]
                )
                
                set_default_btn.click(
                    fn=set_default_model,
                    inputs=[model_name_input],
                    outputs=[model_config_status, model_paths_table, model_type]
                )
                
                # 页面加载时自动加载模型配置
                app.load(
                    fn=load_model_paths,
                    inputs=None,
                    outputs=[model_paths_table, model_type]
                )
            
            # 数据管理面板
            with gr.Tab("数据管理"):
                with gr.Row():
                    with gr.Column(scale=1):
                        gr.Markdown("### 数据输入方式")
                        data_input_type = gr.Radio(
                            choices=["上传文件", "本地路径"],
                            label="选择数据输入方式",
                            value="上传文件"
                        )
                    
                    with gr.Column(scale=2):
                        with gr.Group(visible=True) as upload_group:
                            data_files = gr.File(
                                label="上传数据文件",
                                file_types=[".json", ".jsonl", ".xlsx", ".xls"],
                                file_count="multiple"
                            )
                        
                        with gr.Group(visible=False) as path_group:
                            data_path = gr.Textbox(
                                label="本地数据文件路径",
                                placeholder="例如：/path/to/data.json 或 C:\\path\\to\\data.json",
                                lines=1
                            )
                            data_format = gr.Radio(
                                choices=["json", "jsonl", "excel"],
                                label="文件格式",
                                value="json"
                            )
                
                with gr.Row():
                    with gr.Column():
                        gr.Markdown("### 字段映射")
                        with gr.Row():
                            question_field = gr.Textbox(
                                label="问题字段名",
                                value="question",
                                placeholder="输入源数据中问题的字段名"
                            )
                            answer_field = gr.Textbox(
                                label="答案字段名",
                                value="answer",
                                placeholder="输入源数据中答案的字段名"
                            )
                        
                        with gr.Row():
                            cot_field = gr.Textbox(
                                label="思考过程字段名",
                                value="cot",
                                placeholder="输入源数据中思考过程的字段名，可选"
                            )
                            background_field = gr.Textbox(
                                label="背景知识字段名",
                                value="background",
                                placeholder="输入源数据中背景知识的字段名，可选"
                            )
                        
                        cot_format = gr.Radio(
                            choices=["think", "complex", "none"],
                            label="思考过程格式",
                            value="complex",
                            info="think: 简单思考过程, complex: 复杂思考过程, none: 无思考过程"
                        )
                    
                    with gr.Column():
                        data_split_ratio = gr.Slider(
                            minimum=0.5, maximum=0.9, step=0.05,
                            label="训练集比例",
                            value=0.8
                        )
                        
                        process_btn = gr.Button("处理数据", variant="primary")
                        
                        data_output = gr.Textbox(
                            label="处理结果",
                            lines=3
                        )
                
                with gr.Row():
                    data_preview = gr.DataFrame(
                        label="数据预览",
                        headers=["Question", "Answer", "CoT", "Background"],
                        interactive=False
                    )
                    
                with gr.Row():
                    data_preview = gr.DataFrame(
                        label="数据预览",
                        headers=["Question", "Answer", "CoT", "Background"],
                        interactive=False
                    )
                    
                    with gr.Accordion("高级选项", open=False):
                        with gr.Row():
                            output_format = gr.Radio(
                                choices=["json", "jsonl"],
                                label="输出格式",
                                value="json"
                            )
                            
                            expected_count = gr.Number(
                                label="预期数据条数",
                                value=0,
                                info="设置为0表示不检查数据条数"
                            )
                        
                        with gr.Row():
                            timeout = gr.Number(
                                label="处理超时时间(秒)",
                                value=60
                            )
                            
                            max_samples = gr.Number(
                                label="最大处理样本数",
                                value=0,
                                info="设置为0表示处理所有数据"
                            )
                
                # 添加UI交互逻辑 - 移动到函数内部
                def toggle_input_groups(choice):
                    if choice == "上传文件":
                        return gr.update(visible=True), gr.update(visible=False)
                    else:
                        return gr.update(visible=False), gr.update(visible=True)

                data_input_type.change(
                    fn=toggle_input_groups,
                    inputs=[data_input_type],
                    outputs=[upload_group, path_group]
                )

                process_btn.click(
                    fn=process_uploaded_data,
                    inputs=[
                        data_input_type, data_files, data_path, data_format,
                        question_field, answer_field, cot_field, background_field,
                        cot_format, data_split_ratio, output_format, expected_count, timeout, max_samples
                    ],
                    outputs=[data_output, data_preview]
                )
            
            # SFT训练面板
            with gr.Tab("SFT训练"):
                with gr.Row():
                    with gr.Column():
                        sft_batch_size = gr.Slider(
                            minimum=1, maximum=16, step=1,
                            label="批次大小",
                            value=4
                        )
                        
                        sft_lr = gr.Textbox(
                            label="学习率（科学计数法，例如：2e-4）",
                            value="2e-4",
                            placeholder="例如：1e-4, 2e-4, 5e-5"
                        )
                        
                        sft_epochs = gr.Slider(
                            minimum=1, maximum=10, step=1,
                            label="训练轮数",
                            value=3
                        )
                        
                        sft_grad_accum = gr.Slider(
                            minimum=1, maximum=8, step=1,
                            label="梯度累积步数",
                            value=2
                        )
                        
                    with gr.Column():
                        sft_output_dir = gr.Textbox(
                            label="输出目录",
                            value="outputs/sft_model",
                            lines=1
                        )
                        
                        sft_start_btn = gr.Button("开始SFT训练", variant="primary")
                        sft_stop_btn = gr.Button("停止训练", variant="stop")
                        
                        sft_status = gr.Textbox(
                            label="训练状态",
                            value="未开始",
                            lines=3
                        )
            
            # RL训练面板
            with gr.Tab("RL训练"):
                with gr.Row():
                    with gr.Column():
                        rl_batch_size = gr.Slider(
                            minimum=1, maximum=16, step=1,
                            label="批次大小",
                            value=2
                        )
                        
                        rl_lr = gr.Textbox(
                            label="学习率（科学计数法，例如：1e-5）",
                            value="1e-5",
                            placeholder="例如：1e-5, 5e-6, 2e-5"
                        )
                        
                        rl_max_steps = gr.Slider(
                            minimum=10, maximum=1000, step=10,
                            label="最大训练步数",
                            value=100
                        )
                        
                        rl_beta = gr.Slider(
                            minimum=0.05, maximum=0.5, step=0.05,
                            label="KL惩罚系数",
                            value=0.1
                        )
                        
                    with gr.Column():
                        rl_output_dir = gr.Textbox(
                            label="输出目录",
                            value="outputs/rl_model",
                            lines=1
                        )
                        
                        # 奖励权重设置
                        with gr.Row():
                            rl_format_weight = gr.Slider(
                                minimum=0, maximum=1, step=0.1,
                                label="格式奖励权重",
                                value=0.3
                            )
                            
                            rl_semantic_weight = gr.Slider(
                                minimum=0, maximum=1, step=0.1,
                                label="语义奖励权重",
                                value=0.3
                            )
                            
                            rl_reasoning_weight = gr.Slider(
                                minimum=0, maximum=1, step=0.1,
                                label="推理奖励权重",
                                value=0.4
                            )
                        
                        rl_gpu_memory = gr.Slider(
                            minimum=0.1, maximum=0.9, step=0.1,
                            label="GPU显存使用率",
                            value=0.6
                        )
                        
                        rl_start_btn = gr.Button("开始RL训练", variant="primary")
                        rl_stop_btn = gr.Button("停止训练", variant="stop")
                        
                        rl_status = gr.Textbox(
                            label="训练状态",
                            value="未开始",
                            lines=3
                        )
            
            # 训练监控面板
            with gr.Tab("训练监控"):
                with gr.Row():
                    # 刷新按钮
                    refresh_btn = gr.Button("刷新数据")
                    
                    # 自动刷新开关
                    auto_refresh = gr.Checkbox(label="自动刷新(每3秒)", value=True)
                    
                    # 显示当前GPU信息
                    current_gpu_info = gr.JSON(label="GPU状态", value=get_gpu_info())
                
                with gr.Row():
                    # 损失曲线
                    loss_chart = gr.Plot(label="训练损失")
                    
                    # GPU利用率
                    gpu_chart = gr.Plot(label="GPU利用率")
                    
                    # 学习率
                    lr_chart = gr.Plot(label="学习率变化")
                
                # 训练日志表格
                training_log = gr.Dataframe(
                    headers=["步数", "时间", "损失", "学习率", "每步时间(s)", "GPU利用率(%)", "显存使用率(%)", "状态"],
                    label="训练日志"
                )
                
                # 定义刷新函数
                def refresh_charts():
                    global CURRENT_LOG
                    # 如果没有日志数据，返回空图表
                    if not CURRENT_LOG:
                        empty_fig = go.Figure()
                        empty_fig.update_layout(
                            title="无训练数据",
                            xaxis_title="步数",
                            yaxis_title="数值",
                            template="plotly_white"
                        )
                        return empty_fig, empty_fig, empty_fig, get_gpu_info(), []
                    
                    # 准备图表数据
                    loss_fig = create_loss_chart(CURRENT_LOG)
                    gpu_fig = create_gpu_chart(CURRENT_LOG)
                    lr_fig = create_lr_chart(CURRENT_LOG)
                    
                    # 准备表格数据
                    table_data = []
                    # 反转日志，最新的条目在前面
                    for log in reversed(CURRENT_LOG):
                        table_data.append([
                            log.get("step", 0),
                            log.get("time", ""),
                            round(log.get("loss", 0), 6),
                            log.get("learning_rate", 0),
                            round(log.get("step_time", 0), 3),
                            round(log.get("gpu_util", 0), 1),
                            round(log.get("gpu_mem", 0), 1),
                            log.get("status", "运行中")
                        ])
                    
                    return loss_fig, gpu_fig, lr_fig, get_gpu_info(), table_data
                
                # 使用HTML和JavaScript实现兼容所有Gradio版本的自动刷新
                autorefresh_html = gr.HTML("""
                <script>
                    // 等待页面加载完成
                    document.addEventListener('DOMContentLoaded', function() {
                        let refreshInterval;
                        
                        // 检查元素是否已加载到DOM中
                        function checkElements() {
                            const refreshBtn = document.querySelector('button:contains("刷新数据")');
                            const autoRefreshCheckbox = document.querySelector('input[type="checkbox"]');
                            
                            if (refreshBtn && autoRefreshCheckbox) {
                                setupRefresh(refreshBtn, autoRefreshCheckbox);
                            } else {
                                // 如果元素还没加载，50ms后再试
                                setTimeout(checkElements, 50);
                            }
                        }
                        
                        function setupRefresh(refreshBtn, checkbox) {
                            // 初始设置定时器
                            if (checkbox.checked) {
                                refreshInterval = setInterval(function() {
                                    refreshBtn.click();
                                }, 3000);
                            }
                            
                            // 监听复选框变化
                            checkbox.addEventListener('change', function() {
                                if (this.checked) {
                                    refreshInterval = setInterval(function() {
                                        refreshBtn.click();
                                    }, 3000);
                                } else {
                                    clearInterval(refreshInterval);
                                }
                            });
                        }
                        
                        // 开始检查元素
                        checkElements();
                    });
                    
                    // 另一种方法，使用突变观察器
                    const observer = new MutationObserver(function(mutations) {
                        for (const mutation of mutations) {
                            if (mutation.type === 'childList') {
                                const refreshBtn = document.querySelector('button:contains("刷新数据")');
                                const autoRefreshCheckbox = document.querySelector('input[type="checkbox"]');
                                
                                if (refreshBtn && autoRefreshCheckbox) {
                                    let refreshInterval;
                                    
                                    // 初始设置定时器
                                    if (autoRefreshCheckbox.checked) {
                                        refreshInterval = setInterval(function() {
                                            refreshBtn.click();
                                        }, 3000);
                                    }
                                    
                                    // 监听复选框变化
                                    autoRefreshCheckbox.addEventListener('change', function() {
                                        if (this.checked) {
                                            refreshInterval = setInterval(function() {
                                                refreshBtn.click();
                                            }, 3000);
                                        } else {
                                            clearInterval(refreshInterval);
                                        }
                                    });
                                    
                                    observer.disconnect();
                                    break;
                                }
                            }
                        }
                    });
                    
                    // 开始观察
                    observer.observe(document.body, { childList: true, subtree: true });
                </script>
                """)
                
                # 刷新按钮点击事件
                refresh_btn.click(
                    fn=refresh_charts,
                    inputs=[],
                    outputs=[loss_chart, gpu_chart, lr_chart, current_gpu_info, training_log]
                )
            
            # 模型评估面板
            with gr.Tab("模型评估"):
                with gr.Row():
                    with gr.Column(scale=1):
                        # 模型加载控制区
                        gr.Markdown("### 模型控制")
                        
                        eval_model_path = gr.Dropdown(
                            choices=["Qwen2.5-1.5B-Instruct", "DeepSeek-R1-Distill-Qwen-1.5B"],
                            label="选择基础模型",
                            value="Qwen2.5-1.5B-Instruct"
                        )
                        
                        # 添加自定义模型路径配置
                        custom_model_path = gr.Textbox(
                            label="自定义模型路径（优先于选择的基础模型）",
                            placeholder="例如：/path/to/model 或 C:\\path\\to\\model",
                            value="",
                            lines=1
                        )
                        
                        eval_lora_dir = gr.Textbox(
                            label="LoRA模型目录 (可选)",
                            value="",
                            lines=1
                        )
                        
                        with gr.Row():
                            load_model_btn = gr.Button("加载模型", variant="primary")
                            unload_model_btn = gr.Button("卸载模型", variant="secondary")
                        
                        model_status = gr.Textbox(
                            label="模型状态",
                            value="未加载模型",
                            lines=2,
                            interactive=False
                        )
                        
                        eval_max_len = gr.Slider(
                            minimum=128, maximum=4096, step=128,
                            label="最大生成长度",
                            value=1024
                        )
                        
                    with gr.Column(scale=2):
                        gr.Markdown("### 模型评估")
                        
                        eval_question = gr.TextArea(
                            label="输入问题",
                            placeholder="请输入要评估的问题...",
                            lines=5
                        )
                        
                        eval_btn = gr.Button("开始评估", variant="primary")
                        eval_with_loaded_btn = gr.Button("使用已加载模型评估", variant="primary")
                        
                        eval_result = gr.TextArea(
                            label="评估结果",
                            lines=15
                        )
            
            # CoT数据生成面板 - 更紧凑的布局
            with gr.Tab("CoT数据生成"):
                # 添加标题和说明
                gr.Markdown("""## 思维链(CoT)数据生成工具""")
                
                # 添加带样式的CSS
                gr.HTML("""
                <style>
                .important-field { border-left: 4px solid #2196F3; padding-left: 8px; }
                .tech-container { background: linear-gradient(to right, rgba(25,118,210,0.05), transparent); border-radius: 8px; padding: 10px; margin-bottom: 8px; }
                .header-tech { background: linear-gradient(to right, #1565C0, #03A9F4); color: white; padding: 5px 10px; border-radius: 4px; margin-bottom: 10px; font-weight: bold; }
                .compact-group { padding: 5px !important; margin-bottom: 5px !important; }
                .compact-row { gap: 5px !important; }
                .file-error { color: #f44336; font-weight: bold; }
                .file-success { color: #4CAF50; font-weight: bold; }
                .file-warning { color: #FF9800; font-weight: bold; }
                </style>
                """)
                
                # 使用三列布局，更紧凑
                with gr.Row():
                    # 第一列：模型配置
                    with gr.Column(scale=1):
                        with gr.Group(elem_classes=["tech-container", "compact-group"]):
                            gr.HTML('<div class="header-tech">模型配置</div>')
                            
                            cot_model_type = gr.Radio(
                                choices=["v3", "r1", "local"],
                                label="模型类型",
                                value="local",
                                info="v3: DeepSeek-V3, r1: DeepSeek-R1, local: 本地模型"
                            )
                            
                            with gr.Row(elem_classes=["compact-row"]):
                                cot_api_key = gr.Textbox(
                                    label="API密钥",
                                    placeholder="API密钥",
                                    type="password"
                                )
                                
                                no_api_key = gr.Checkbox(
                                    label="不使用API密钥",
                                    value=False
                                )
                            
                            cot_base_url = gr.Textbox(
                                label="API基础URL",
                                placeholder="例如: http://localhost:8000/v1",
                                value="http://*************:7862/v1/"
                            )
                            
                            with gr.Group(visible=True, elem_classes=["compact-group"]) as local_model_group:
                                cot_local_model_name = gr.Textbox(
                                    label="本地模型名称",
                                    placeholder="模型名称或路径",
                                    value="Qwen2.5-32B-Instruct-GPTQ-Int4"
                                )
                                
                                with gr.Row(elem_classes=["compact-row"]):
                                    cot_format = gr.Radio(
                                        choices=["think", "custom"],
                                        label="思维链格式",
                                        value="think"
                                    )
                                    
                                    cot_with_answer = gr.Checkbox(
                                        label="提供答案生成推理",
                                        value=True
                                    )
                                
                                cot_timeout = gr.Number(
                                    label="请求超时(秒)",
                                    value=120
                                )
                    
                    # 第二列：输入/输出配置
                    with gr.Column(scale=1):
                        with gr.Group(elem_classes=["tech-container", "compact-group"]):
                            gr.HTML('<div class="header-tech">输入/输出配置</div>')
                            
                            with gr.Row(elem_classes=["compact-row"]):
                                cot_input_file = gr.Textbox(
                                    label="输入文件路径",
                                    placeholder="source_data/input.json",
                                    value="",
                                    elem_classes=["important-field"]
                                )
                                cot_input_upload = gr.File(
                                    label="上传源数据",
                                    file_types=[".json", ".jsonl", ".xlsx", ".csv"],
                                    file_count="single"
                                )
                            
                            cot_output_file = gr.Textbox(
                                label="输出文件路径",
                                placeholder="data/output.json",
                                value="data/cot_data.json"
                            )
                            
                            with gr.Row(elem_classes=["compact-row"]):
                                cot_output_format = gr.Radio(
                                    choices=["auto", "json", "jsonl"],
                                    label="输出格式",
                                    value="json",
                                    interactive=True
                                )
                            
                            with gr.Row(elem_classes=["compact-row"]):
                                cot_question_field = gr.Textbox(
                                    label="问题字段名",
                                    placeholder="问题字段",
                                    value="instruction"
                                )
                                cot_response_field = gr.Textbox(
                                    label="回答字段名",
                                    placeholder="回答字段",
                                    value="output"
                                )
                            
                            with gr.Row(elem_classes=["compact-row"]):
                                cot_background_knowledge = gr.Textbox(
                                    label="背景知识文件",
                                    placeholder="背景知识文件路径",
                                    scale=3
                                )
                                cot_bg_upload = gr.File(
                                    label="上传背景知识",
                                    file_types=[".txt", ".md"],
                                    file_count="single",
                                    scale=1
                                )
                    
                    # 第三列：批处理设置和操作按钮
                    with gr.Column(scale=1):
                        # 批处理设置
                        with gr.Group(elem_classes=["tech-container", "compact-group"]):
                            gr.HTML('<div class="header-tech">批处理设置</div>')
                            
                            cot_batch_size = gr.Slider(
                                minimum=1, maximum=10, step=1,
                                label="并发处理数",
                                value=1,
                                info="同时处理的条目数"
                            )
                            
                            cot_max_retries = gr.Slider(
                                minimum=1, maximum=5, step=1,
                                label="最大重试次数",
                                value=3
                            )
                            
                            cot_max_samples = gr.Number(
                                label="最大处理样本数",
                                value=0,
                                info="0表示处理所有数据"
                            )
                        
                        # 状态显示
                        cot_status = gr.HTML(
                            label="生成状态",
                            value="<span>未开始</span>"
                        )
                        
                        # 操作按钮
                        with gr.Group(elem_classes=["compact-group"]):
                            with gr.Row():
                                cot_generate_btn = gr.Button("🚀 开始生成", variant="primary", scale=2)
                                cot_stop_btn = gr.Button("⏹️ 停止", variant="stop", scale=1)
                                cot_refresh_btn = gr.Button("🔄 刷新预览", variant="secondary", scale=1)
                
                # 数据预览
                gr.HTML('<div class="header-tech">生成结果预览</div>')
                cot_preview = gr.DataFrame(
                    label="数据预览",
                    headers=["问题", "思维链", "回答"],
                    value=[]
                )
                
                # 注册事件处理
                cot_input_upload.upload(
                    fn=handle_input_upload,
                    inputs=[cot_input_upload],
                    outputs=[cot_input_file, cot_status]
                )
                
                cot_bg_upload.upload(
                    fn=handle_bg_upload,
                    inputs=[cot_bg_upload],
                    outputs=[cot_background_knowledge, cot_status]
                )
                
                cot_refresh_btn.click(
                    fn=refresh_cot_preview,
                    inputs=[cot_output_file],
                    outputs=[cot_status, cot_preview]
                )
                
                cot_input_file.change(
                    fn=check_input_file_exists,
                    inputs=[cot_input_file],
                    outputs=[cot_input_file, cot_status]
                )
                
                cot_input_file.change(
                    fn=validate_before_generation,
                    inputs=[cot_input_file],
                    outputs=[cot_generate_btn, cot_status]
                )
                
                cot_model_type.change(
                    fn=toggle_local_model,
                    inputs=[cot_model_type],
                    outputs=[local_model_group]
                )
                
                # 注册生成事件
                cot_generate_btn.click(
                    fn=run_cot_generation,
                    inputs=[
                        cot_model_type, cot_api_key, cot_base_url, cot_input_file, cot_output_file, 
                        cot_output_format, cot_question_field, cot_response_field, cot_batch_size, 
                        cot_max_retries, cot_background_knowledge, cot_max_samples, cot_local_model_name,
                        cot_format, cot_with_answer, cot_timeout, no_api_key
                    ],
                    outputs=[cot_status]
                )
                
                # 注册停止事件
                cot_stop_btn.click(
                    fn=stop_cot_generation,
                    inputs=[],
                    outputs=[cot_status]
                )
        
        # SFT训练按钮事件
        def update_sft_status(result):
            if "错误" in result:
                return result
            else:
                return result + "\n请在训练监控面板查看详细进度"

        sft_start_btn.click(
            fn=lambda *args: update_sft_status(start_sft_training(
                args[0], args[1], args[2], args[3], args[4],
                args[5], parse_learning_rate(args[6]), args[7], args[8],
                args[9], args[10]
            )),
            inputs=[
                model_type, train_mode, system_prompt, lora_rank, lora_alpha,
                sft_batch_size, sft_lr, sft_epochs, sft_grad_accum,
                target_modules, sft_output_dir
            ],
            outputs=[sft_status]
        )
        
        # RL训练按钮事件
        def update_rl_status(result):
            if "错误" in result:
                return result
            else:
                return result + "\n请在训练监控面板查看详细进度"

        rl_start_btn.click(
            fn=lambda *args: update_rl_status(start_rl_training(
                args[0], args[1], args[2], args[3],
                args[4], parse_learning_rate(args[5]), args[6], args[7],
                args[8], args[9], args[10], args[11], args[12]
            )),
            inputs=[
                model_type, train_mode, lora_rank, lora_alpha,
                rl_batch_size, rl_lr, rl_max_steps, rl_beta,
                target_modules, rl_output_dir, rl_format_weight,
                rl_semantic_weight, rl_reasoning_weight, rl_gpu_memory
            ],
            outputs=[rl_status]
        )
        
        # 停止训练按钮事件
        sft_stop_btn.click(
            fn=stop_training,
            inputs=[],
            outputs=[sft_status]
        )
        
        rl_stop_btn.click(
            fn=stop_training,
            inputs=[],
            outputs=[rl_status]
        )
        
        # 评估按钮事件
        eval_btn.click(
            fn=run_evaluation,
            inputs=[eval_model_path, custom_model_path, eval_lora_dir, eval_question, eval_max_len],
            outputs=[eval_result]
        )
        
        # 加载和卸载模型按钮事件
        load_model_btn.click(
            fn=load_model,
            inputs=[eval_model_path, custom_model_path, eval_lora_dir, eval_max_len],
            outputs=[model_status]
        )
        
        unload_model_btn.click(
            fn=unload_model,
            inputs=[],
            outputs=[model_status]
        )
        
        # 使用已加载模型评估按钮事件
        eval_with_loaded_btn.click(
            fn=run_evaluation_with_loaded_model,
            inputs=[eval_question, eval_max_len],
            outputs=[eval_result]
        )
        
        return app

# 命令行参数解析
def parse_args():
    parser = argparse.ArgumentParser(description="DeepSeek训练系统UI")
    parser.add_argument("--port", type=int, default=7860, help="UI服务端口号")
    parser.add_argument("--listen", type=str, default="0.0.0.0", help="UI服务监听地址")
    parser.add_argument("--share", action="store_true", help="是否创建公开链接")
    parser.add_argument("--gpu_id", type=str, default="0", help="指定使用的GPU ID，例如：0,1,2")
    return parser.parse_args()

if __name__ == "__main__":
    args = parse_args()
    # 设置环境变量，指定使用的GPU
    os.environ["CUDA_VISIBLE_DEVICES"] = args.gpu_id
    app = build_ui()
    app.launch(server_name=args.listen, server_port=args.port, share=args.share) 