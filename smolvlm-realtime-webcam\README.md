# 智能视觉语音助手

这是一个基于Web的智能视觉助手，支持实时摄像头和本地视频文件两种模式，用户可以通过语音与AI进行视觉内容的对话。

## 主要功能

### 🎥 双模式支持
- **摄像头模式**：使用设备摄像头进行实时视觉对话
- **视频文件模式**：加载本地视频文件进行基于视频内容的对话

### 🎤 语音交互
- 按住麦克风按钮进行语音输入
- 支持中文和英文语音识别
- 自动语音播报AI回复（可配置）

### 🎬 视频播放控制
- 播放/暂停控制
- 进度条拖拽跳转
- 自动播放下一个视频
- 视频文件列表管理

### 💬 智能对话
- 基于当前视频画面内容进行AI对话
- 支持复杂的视觉理解和分析
- 聊天记录保存和显示

## 使用方法

### 1. 摄像头模式（默认）
1. 打开网页后自动启动摄像头
2. 按住🎤麦克风按钮说话
3. AI会根据摄像头看到的内容回答问题

### 2. 视频文件模式
1. 点击 **📁 选择视频** 按钮
2. 选择一个或多个视频文件（支持多选）
3. 点击 **🎬 视频** 按钮切换到视频模式
4. 使用播放控制器控制视频播放
5. 在视频播放过程中按住麦克风进行提问

### 3. 视频控制操作
- **▶️/⏸️** - 播放/暂停视频
- **进度条** - 点击跳转到指定时间点
- **视频列表** - 点击切换不同视频文件
- **📷 摄像头/🎬 视频** - 切换视频源模式

### 4. 设置配置
点击右上角 ⚙️ 设置按钮可以配置：
- API服务器地址
- TTS语音服务地址
- 语音识别语言（中文/英文）
- 自动语音播报开关

## 支持的视频格式

- MP4
- WebM
- AVI
- MOV
- 以及其他浏览器支持的视频格式

## 技术特性

- **响应式设计**：支持桌面和移动设备
- **实时视频流处理**：高效的视频帧捕获
- **Web Speech API**：原生语音识别支持
- **拖拽文件**：支持多文件选择和管理
- **自动播放**：智能的视频播放队列管理

## 浏览器要求

- 支持WebRTC的现代浏览器
- 摄像头和麦克风权限
- 支持Web Speech API（语音识别）
- 支持HTML5 Video元素

## 使用场景

1. **教育培训**：对视频教学内容进行AI问答
2. **内容分析**：分析视频中的场景、物体、动作等
3. **实时监控**：基于摄像头画面进行智能分析
4. **影视评论**：对电影、视频内容进行AI解读
5. **安防监控**：实时视频内容的智能识别

## 注意事项

- 首次使用需要授权摄像头和麦克风权限
- 视频文件过大可能影响加载速度
- 语音识别需要网络连接
- 建议在安静环境下使用语音功能

## 快速开始

1. 直接打开 `voice_chat.html` 文件
2. 授权摄像头和麦克风权限
3. 开始与AI进行视觉对话！

---

*该项目基于现代Web技术构建，提供流畅的用户体验和强大的AI视觉理解能力。*

