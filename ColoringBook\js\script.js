document.addEventListener('DOMContentLoaded', () => {

    // --- 元素获取 (吉卜力风格) ---
    const promptInput = document.getElementById('prompt-input');
    const ratioButtons = document.getElementById('ratio-buttons');
    const countInput = document.getElementById('count-input');
    const generateBtn = document.getElementById('generate-btn');
    const selectModeToggle = document.getElementById('select-mode-toggle');
    const printBtn = document.getElementById('print-btn');
    const galleryGrid = document.getElementById('gallery-grid');

    // --- 状态管理 ---
    let selectedRatio = '1:1';
    let isSelectMode = false;

    // --- 事件监听 ---

    // F2.1: 比例选择 (木制符文)
    ratioButtons.addEventListener('click', (e) => {
        const button = e.target.closest('.rune-btn');
        if (button) {
            ratioButtons.querySelectorAll('.rune-btn').forEach(btn => btn.classList.remove('active'));
            button.classList.add('active');
            selectedRatio = button.dataset.ratio;
        }
    });

    // F4.2: 选择模式切换 (萤火虫开关)
    selectModeToggle.addEventListener('change', (e) => {
        isSelectMode = e.target.checked;
        updatePrintButtonState();
        if (!isSelectMode) {
            galleryGrid.querySelectorAll('.gallery-frame.selected').forEach(card => card.classList.remove('selected'));
            updatePrintButtonState(); // 更新按钮状态
        }
    });

    // F3.1: 生成图片 (施展魔法)
    generateBtn.addEventListener('click', () => {
        const prompt = promptInput.value;
        const count = parseInt(countInput.value, 10);

        if (!prompt) {
            alert('请在魔法卷轴上写下你的想象！');
            return;
        }

        // 模拟API调用与煤灰精灵动画
        setLoading(true);
        setTimeout(() => {
            for (let i = 0; i < count; i++) {
                const imageUrl = `https://picsum.photos/512/512?random=${Date.now() + i}`;
                createImageCard(imageUrl);
            }
            setLoading(false);
            updatePrintButtonState(); // 生成后更新打印按钮计数
        }, 2500); // 魔法需要一点时间
    });
    
    // F5: 打印按钮
    printBtn.addEventListener('click', () => {
        const selectedFrames = galleryGrid.querySelectorAll('.gallery-frame.selected');
        if (isSelectMode && selectedFrames.length > 0) {
            printImages(Array.from(selectedFrames).map(frame => frame.querySelector('img').src));
        } else {
            const allFrames = galleryGrid.querySelectorAll('.gallery-frame');
            if(allFrames.length > 0){
                printImages(Array.from(allFrames).map(frame => frame.querySelector('img').src));
            } else {
                alert("画廊里还没有画作可以打印哦！");
            }
        }
    });

    // --- 功能函数 ---

    /**
     * F3.3: 创建画框并添加到画廊
     * @param {string} imageUrl - 图片的URL
     */
    function createImageCard(imageUrl) {
        const frame = document.createElement('div');
        frame.className = 'gallery-frame transform transition-all duration-500 opacity-0 scale-90';

        frame.innerHTML = `
            <img src="${imageUrl}" alt="生成的图片" class="w-full h-full object-cover rounded-md">
            <div class="flex justify-around items-center mt-4">
                <button class="rune-btn text-sm p-2">🎨</button>
                <button class="rune-btn text-sm p-2 delete-btn" style="background-color: #C75D5D; color: white;">🗑️</button>
            </div>
        `;

        // F4.1: 删除按钮事件
        frame.querySelector('.delete-btn').addEventListener('click', (e) => {
            e.stopPropagation(); // 防止触发画框的选择事件
            frame.classList.add('opacity-0', 'scale-0');
            setTimeout(() => {
                frame.remove();
                updatePrintButtonState();
            }, 500);
        });

        // 画框点击事件（用于选择模式）
        frame.addEventListener('click', () => {
            if (isSelectMode) {
                frame.classList.toggle('selected');
                updatePrintButtonState();
            }
        });

        galleryGrid.appendChild(frame);
        // 延迟一小段时间再应用最终样式，以触发CSS过渡动画
        setTimeout(() => {
            frame.classList.remove('opacity-0', 'scale-90');
        }, 50);
    }

    /**
     * 更新打印按钮的状态和文本
     */
    function updatePrintButtonState() {
        const selectedCount = galleryGrid.querySelectorAll('.gallery-frame.selected').length;
        if (isSelectMode && selectedCount > 0) {
            printBtn.textContent = `🖨️ 打印所选 (${selectedCount})`;
        } else {
            const allCount = galleryGrid.querySelectorAll('.gallery-frame').length;
            printBtn.textContent = `🖨️ 打印全部 (${allCount})`;
        }
    }
    
    /**
     * 打印指定的图片
     * @param {string[]} imageUrls - 要打印的图片URL数组
     */
    function printImages(imageUrls) {
        const printWindow = window.open('', '_blank');
        printWindow.document.write('<html><head><title>打印预览</title><style>@media print { body { margin: 0; font-family: sans-serif; } h1 { text-align: center; font-size: 20px; margin-bottom: 20px; } .print-grid { display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 20px; padding: 20px; } img { width: 100%; border-radius: 10px; box-shadow: 0 4px 8px rgba(0,0,0,0.1); page-break-inside: avoid; } }</style></head><body>');
        printWindow.document.write('<h1>魔法画册打印预览</h1>');
        printWindow.document.write('<div class="print-grid">');
        imageUrls.forEach(url => {
            printWindow.document.write(`<img src="${url}" />`);
        });
        printWindow.document.write('</div>');
        printWindow.document.write('</body></html>');
        printWindow.document.close();
        setTimeout(() => { 
            printWindow.focus();
            printWindow.print();
            // printWindow.close(); // 在某些浏览器中可能会阻止打印对话框
        }, 500);
    }

    /**
     * 设置加载状态
     * @param {boolean} isLoading - 是否正在加载
     */
    function setLoading(isLoading) {
        if (isLoading) {
            generateBtn.disabled = true;
            generateBtn.textContent = '施法中...';
            // 在这里可以添加煤灰精灵动画的显示逻辑
        } else {
            generateBtn.disabled = false;
            generateBtn.textContent = '施展魔法';
            // 在这里可以添加煤灰精灵动画的隐藏逻辑
        }
    }
});