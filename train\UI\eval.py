import re
import argparse
from datasets import Dataset
import json
from unsloth import FastLanguageModel
from unsloth import is_bfloat16_supported
from vllm import SamplingParams
import os
import torch

# 导入模型配置
try:
    from model_config import get_model_path, get_model_names
except ImportError:
    # 如果找不到model_config模块，提供基本实现
    def get_model_path(model_name):
        model_paths = {
            "origin": "/models/Qwen2.5-1.5B-Instruct",
            "distill": "/models/DeepSeek-R1-Distill-Qwen-1.5B",
            "Qwen2.5-1.5B-Instruct": "/models/Qwen2.5-1.5B-Instruct",
            "DeepSeek-R1-Distill-Qwen-1.5B": "/models/DeepSeek-R1-Distill-Qwen-1.5B"
        }
        return model_paths.get(model_name, model_name)
    
    def get_model_names():
        return ["Qwen2.5-1.5B-Instruct", "DeepSeek-R1-Distill-Qwen-1.5B"]

# 定义模型权重路径
model_weight_paths = {
    "origin": "/models/Qwen2.5-1.5B-Instruct",
    "distill": "/models/DeepSeek-R1-Distill-Qwen-1.5B"
}

# 系统提示词
RL_SYSTEM_PROMPT = """
Respond in the following format:
<think>
推理过程
</think>
最终答案
"""

SFT_SYSTEM_PROMPT = """
You are a helpful assistant.
The assistant first thinks about the reasoning process in the mind and then provides the user with the answer.
The reasoning process enclosed within <think> </think> tags, and answer after </think> tag, respectively, i.e., <think> reasoning process here </think> answer here.
"""

SFT_EMPTY_SYSTEM_PROMPT = """
You are a helpful assistant.
"""

# 全局变量，用于存储已加载的模型
LOADED_MODEL = None
LOADED_TOKENIZER = None
LOADED_MODEL_PATH = None
LOADED_LORA_PATH = None

def eval_with_model(model, tokenizer, question, temperature=0.8, top_p=0.95, max_tokens=1024, system_prompt=None):
    """
    使用已加载的模型和tokenizer进行评估
    
    Args:
        model: 已加载的模型
        tokenizer: 已加载的tokenizer
        question: 用户问题
        temperature: 采样温度
        top_p: top_p采样参数
        max_tokens: 最大生成token数
        system_prompt: 可选的系统提示词
        
    Returns:
        生成的回答文本
    """
    try:
        # 构建消息
        messages = []
        if system_prompt:
            messages.append({'role': 'system', 'content': system_prompt})
        messages.append({'role': 'user', 'content': question})
        
        # 使用chat_template拼接信息
        text = tokenizer.apply_chat_template(
            messages, tokenize=False, add_generation_prompt=True
        )
        
        # 定义采样参数
        sampling_params = SamplingParams(
            temperature=temperature,
            top_p=top_p,
            max_tokens=max_tokens,
        )
        
        # 生成回答
        output = model.fast_generate(
            text,
            sampling_params=sampling_params,
        )[0].outputs[0].text
        
        return output
        
    except Exception as e:
        import traceback
        return f"使用已加载模型评估失败: {str(e)}\n{traceback.format_exc()}"

def load_model_for_eval(model_path, lora_dir=None, max_seq_length=2048):
    """
    加载模型和tokenizer，可选地加载LoRA权重
    
    Args:
        model_path: 模型路径
        lora_dir: LoRA权重目录（可选）
        max_seq_length: 最大序列长度
        
    Returns:
        (model, tokenizer)元组或错误消息
    """
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    try:
        print(f"正在加载模型: {model_path}")
        
        # 加载模型和分词器
        model, tokenizer = FastLanguageModel.from_pretrained(
            model_name=model_path,
            max_seq_length=max_seq_length,
            load_in_4bit=True,
            fast_inference=True,
        )
        
        # 如果有LoRA权重，加载LoRA
        if lora_dir and lora_dir.strip():
            print(f"正在加载LoRA权重: {lora_dir}")
            
            # 读取adapter_config.json文件获取LoRA参数
            adapter_config_path = None
            for filename in os.listdir(lora_dir):
                if filename.endswith("_adapter_config.json"):
                    adapter_config_path = os.path.join(lora_dir, filename)
                    break
                    
            if adapter_config_path and os.path.exists(adapter_config_path):
                try:
                    with open(adapter_config_path, 'r') as f:
                        adapter_config = json.load(f)
                        
                    # 从配置文件中获取参数
                    lora_r = adapter_config.get('r', 8)
                    lora_alpha = adapter_config.get('lora_alpha', 16)
                    target_modules = adapter_config.get('target_modules', 
                                                       ["q_proj", "k_proj", "v_proj", "o_proj", 
                                                        "gate_proj", "up_proj", "down_proj"])
                    
                    print(f"从配置加载LoRA参数: r={lora_r}, alpha={lora_alpha}, 目标模块={target_modules}")
                except Exception as e:
                    print(f"读取adapter_config.json失败: {e}，使用默认LoRA参数")
                    lora_r = 8
                    lora_alpha = 16
                    target_modules = ["gate_proj", "up_proj", "down_proj"]
            else:
                print(f"找不到adapter_config.json文件，使用默认LoRA参数")
                lora_r = 8
                lora_alpha = 16
                target_modules = ["gate_proj", "up_proj", "down_proj"]
                
            # 检查基础模型是否匹配
            if adapter_config_path and os.path.exists(adapter_config_path):
                try:
                    with open(adapter_config_path, 'r') as f:
                        adapter_config = json.load(f)
                    base_model_path = adapter_config.get('base_model_name_or_path', '')
                    if base_model_path and base_model_path != model_path:
                        print(f"警告: LoRA训练的基础模型({base_model_path})与当前加载的模型({model_path})不匹配")
                except Exception as e:
                    print(f"读取基础模型信息失败: {e}")
                
            # 初始化lora模型
            model = FastLanguageModel.get_peft_model(
                model,
                r=lora_r, 
                target_modules=target_modules,
                lora_alpha=lora_alpha,
                use_gradient_checkpointing="unsloth",
                random_state=3407,
            )
            
            # 检查LoRA模型文件是否存在
            adapter_model_path = None
            for filename in os.listdir(lora_dir):
                if filename.endswith("_adapter_model.bin"):
                    adapter_model_path = os.path.join(lora_dir, filename)
                    break
                    
            if adapter_model_path and os.path.exists(adapter_model_path):
                # 检查文件大小，如果太小可能是模拟生成的文件
                file_size = os.path.getsize(adapter_model_path)
                
                # 先尝试加载文件，检查是否是占位文件
                try:
                    weights = torch.load(adapter_model_path, map_location="cpu")
                    if isinstance(weights, dict) and weights.get("is_placeholder", False):
                        print(f"警告: 检测到LoRA占位文件({adapter_model_path})，这不是真实训练权重")
                        print(f"消息: {weights.get('message', '未知')}")
                        print(f"生成时间: {weights.get('created_at', '未知')}")
                        print(f"这可能是在UI环境中模拟训练生成的，不会对模型产生真实影响")
                except Exception as e:
                    # 如果加载失败，回到检查文件大小的逻辑
                    if file_size < 10000:  # 小于10KB可能是测试文件
                        print(f"警告: LoRA模型文件({adapter_model_path})大小只有{file_size}字节，可能是测试文件而非真实权重")
            
            # 预加载LoRA权重到内存并应用
            model.load_lora(lora_dir)
            LOADED_LORA_PATH = lora_dir
            
        # 更新全局变量
        LOADED_MODEL = model
        LOADED_TOKENIZER = tokenizer
        LOADED_MODEL_PATH = model_path
        
        return model, tokenizer
        
    except Exception as e:
        import traceback
        error_msg = f"加载模型失败: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return None, None

def unload_model_for_eval():
    """
    卸载已加载的模型
    
    Returns:
        成功消息或错误消息
    """
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    if LOADED_MODEL is None:
        return "没有已加载的模型"
    
    try:
        # 释放模型资源
        del LOADED_MODEL
        del LOADED_TOKENIZER
        
        # 强制进行垃圾回收
        import gc
        gc.collect()
        
        if torch.cuda.is_available():
            torch.cuda.empty_cache()
            
        model_path = LOADED_MODEL_PATH
        lora_path = LOADED_LORA_PATH
        
        # 重置全局变量
        LOADED_MODEL = None
        LOADED_TOKENIZER = None
        LOADED_MODEL_PATH = None
        LOADED_LORA_PATH = None
        
        return f"已卸载模型: {model_path}" + (f"\n已卸载LoRA权重: {lora_path}" if lora_path else "")
        
    except Exception as e:
        import traceback
        error_msg = f"卸载模型失败: {str(e)}\n{traceback.format_exc()}"
        print(error_msg)
        return error_msg

def eval(args):
    """
    根据命令行参数评估语言模型。
    支持使用已加载的模型或加载新模型。
    """
    global LOADED_MODEL, LOADED_TOKENIZER, LOADED_MODEL_PATH, LOADED_LORA_PATH
    
    try:
        # 检查是否要使用已加载的模型
        use_loaded_model = False
        if hasattr(args, 'use_loaded_model') and args.use_loaded_model and LOADED_MODEL is not None and LOADED_TOKENIZER is not None:
            use_loaded_model = True
            print(f"使用已加载的模型: {LOADED_MODEL_PATH}" + 
                  (f" (带LoRA: {LOADED_LORA_PATH})" if LOADED_LORA_PATH else ""))
            model, tokenizer = LOADED_MODEL, LOADED_TOKENIZER
        else:
            # 确定使用哪个模型路径
            model_path = None
            if hasattr(args, 'model_path') and args.model_path:
                model_path = args.model_path
                print(f"使用指定的模型路径: {model_path}")
            elif hasattr(args, 'model_name') and args.model_name:
                model_path = get_model_path(args.model_name)
                print(f"使用配置的模型路径: {args.model_name} -> {model_path}")
            else:
                # 兼容旧版本参数
                model_path = get_model_path(args.train_mode)
                print(f"使用训练模式对应的模型路径: {args.train_mode} -> {model_path}")
            
            # 检查模型路径是否存在
            if not os.path.exists(model_path):
                return f"错误: 模型路径不存在: {model_path}"
            
            # 加载模型和分词器
            model, tokenizer = FastLanguageModel.from_pretrained(
                model_name = model_path,
                max_seq_length = args.max_seq_length,
                load_in_4bit = True, # False for LoRA 16bit
                fast_inference = True, # Enable vLLM fast inference
                max_lora_rank = args.lora_rank if hasattr(args, 'lora_rank') else 8,
            )

        # 根据args配置，选择系统提示词
        system_prompt = None
        if hasattr(args, 'type') and args.type == "rl" and hasattr(args, 'train_mode') and args.train_mode == "orgin":
            system_prompt = RL_SYSTEM_PROMPT
        elif hasattr(args, 'type') and args.type == 'sft' and hasattr(args, 'train_mode') and args.train_mode == "origin":
            if hasattr(args, 'system_message') and args.system_message == "empty":
                system_prompt = SFT_EMPTY_SYSTEM_PROMPT
            elif hasattr(args, 'system_message') and args.system_message == "instruction":
                system_prompt = SFT_SYSTEM_PROMPT
        
        messages = []
        if system_prompt:
            messages.append({'role': 'system', 'content': system_prompt})
        messages.append({'role': 'user', 'content': args.question})

        # 使用chat_template拼接信息
        text = tokenizer.apply_chat_template(
            messages, tokenize = False, add_generation_prompt = True)

        # 定义采样参数
        sampling_params = SamplingParams(
            temperature = 0.8,
            top_p = 0.95,
            max_tokens = args.max_tokens if hasattr(args, 'max_tokens') else 1024,
        )

        # 如果有LoRA权重并且还没加载，则加载LoRA
        if not use_loaded_model and hasattr(args, 'lora_dir') and args.lora_dir:
            # 初始化lora模型
            model = FastLanguageModel.get_peft_model(
                model,
                r = args.lora_rank if hasattr(args, 'lora_rank') else 8, 
                target_modules = [
                    "gate_proj", "up_proj", "down_proj",
                ], 
                lora_alpha = args.lora_alpha if hasattr(args, 'lora_alpha') else 16,
                use_gradient_checkpointing = "unsloth", 
                random_state = 3407,
            )    

            # 获取输出
            output = model.fast_generate(
                text,
                sampling_params = sampling_params,
                lora_request = model.load_lora(args.lora_dir),
            )[0].outputs[0].text
            return output
        else:
            # 使用已加载的模型（可能包括LoRA）或不带LoRA的新模型
            output = model.fast_generate(
                text,
                sampling_params = sampling_params,
            )[0].outputs[0].text
            return output
            
    except Exception as e:
        import traceback
        return f"评估失败: {str(e)}\n{traceback.format_exc()}"


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Evaluate a language model with specified parameters.")
    parser.add_argument("--train_mode", type=str, choices=["origin", "distill"], default="origin", help="Training mode: origin or distill.")
    parser.add_argument("--model_name", type=str, choices=get_model_names(), default="Qwen2.5-1.5B-Instruct", help="Model name to use from config.")
    parser.add_argument("--model_path", type=str, default=None, help="Directory for model weights, if not none, default model path will disabled and do not use lora.")
    parser.add_argument("--type", type=str, choices=["rl", "sft"], default="sft", help="Type of training: rl or sft.")
    parser.add_argument("--system_message", type=str, choices=["empty", "instruction"], default="instruction", help="System message type for SFT: empty or instruction.")
    parser.add_argument("--question", type=str, required=True, help="Question to be answered by the model.")
    parser.add_argument("--max_seq_length", type=int, default=2048, help="Maximum sequence length for the model.")
    parser.add_argument("--max_tokens", type=int, default=1024, help="Maximum tokens for generation.")
    parser.add_argument("--lora_rank", type=int, default=8, help="LoRA rank for model fine-tuning.")
    parser.add_argument("--lora_alpha", type=int, default=16, help="LoRA alpha for model fine-tuning.")
    parser.add_argument("--lora_dir", type=str, default=None, help="Directory for LoRA weights.")
    parser.add_argument("--use_loaded_model", action="store_true", help="Use globally loaded model if available.")
    
    args = parser.parse_args()
    print(eval(args))

