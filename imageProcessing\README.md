# 图像处理工具集

这个目录包含了多个图像处理和转换工具，适用于日常的图像优化和格式转换需求。

## 工具列表

### 1. PNG to JPG 转换器 (png_to_jpg_converter.py)
- **功能**: 将PNG图像批量转换为JPG格式
- **特点**: 自动处理透明背景，生成时间戳文件名
- **适用场景**: 减小文件大小，提高web加载速度

### 2. Web格式图像压缩器 (image_web_compressor.py)
- **功能**: 多格式图像转换和压缩工具
- **支持输入格式**: PNG, JPG, JPEG, BMP, TIFF, WebP, HEIC, HEIF
- **支持输出格式**: WebP, AVIF, 优化JPG, 优化PNG
- **特点**: 智能压缩、尺寸调整、透明度处理

### 3. PNG to JPG 转换器 Web版 (png_to_jpg_converter.html)
- **功能**: 浏览器端PNG转JPG转换
- **特点**: 拖拽上传、实时预览、批量下载、ZIP打包
- **优势**: 无需安装软件，数据本地处理，隐私安全

## 安装依赖

```bash
pip install -r requirements.txt
```

## 使用指南

### Web格式压缩器使用示例

```python
from image_web_compressor import ImageWebCompressor

# 创建压缩器实例
compressor = ImageWebCompressor()

# 批量转换为WebP格式
compressor.batch_convert(
    source_dir="原图目录",
    dest_dir="输出目录", 
    output_format='webp',  # webp, avif, jpg, png
    quality=80,            # 压缩质量
    max_width=1920,        # 最大宽度
    max_height=1080,       # 最大高度
    keep_original_names=True  # 保持原文件名
)
```

### 支持的格式对比

| 格式 | 压缩率 | 质量 | 透明度 | 浏览器支持 | 适用场景 |
|------|--------|------|--------|------------|----------|
| WebP | 优秀 | 高 | ✅ | 现代浏览器 | 网站图片优化 |
| AVIF | 极佳 | 极高 | ✅ | 最新浏览器 | 未来标准 |
| JPG | 良好 | 中等 | ❌ | 全部浏览器 | 通用性最强 |
| PNG | 较差 | 无损 | ✅ | 全部浏览器 | 需要透明度 |

### 性能优化建议

1. **WebP格式**: 现代网站首选，平均压缩率提升25-35%
2. **AVIF格式**: 最新标准，压缩率比WebP再提升20%
3. **质量设置**: 
   - 80-90: 高质量，适合重要图片
   - 60-80: 平衡质量与大小
   - 40-60: 小尺寸，适合缩略图
4. **尺寸控制**: 设置max_width/max_height可大幅减小文件大小

### 批量处理技巧

1. **按用途分类**:
   ```python
   # 高质量原图保存
   compressor.batch_convert(..., output_format='png', quality=9)
   
   # 网站展示图片  
   compressor.batch_convert(..., output_format='webp', quality=80)
   
   # 缩略图生成
   compressor.batch_convert(..., output_format='webp', quality=60, max_width=400)
   ```

2. **多格式输出**: 为不同浏览器兼容性生成多个版本

## 注意事项

1. **HEIC支持**: 需要安装 `pillow-heif` 才能处理iPhone照片
2. **AVIF支持**: 需要较新版本的Pillow
3. **内存使用**: 处理大图时注意内存占用
4. **批量处理**: 大量文件建议分批处理

## 常见问题

**Q: 为什么AVIF格式转换失败？**
A: 请确保Pillow版本>=10.0.0，并且系统支持AVIF编解码器

**Q: 如何获得最佳压缩效果？**
A: 建议使用WebP格式，质量设置75-85，同时限制图片尺寸

**Q: 转换后图片变模糊怎么办？**
A: 提高质量参数，或者检查是否设置了过小的尺寸限制

## 更新日志

- v1.0: 基础PNG转JPG功能
- v2.0: 增加Web格式压缩器
- v2.1: 添加浏览器端转换工具
- v2.2: 支持HEIC格式和尺寸调整 