import re
from openai import OpenAI
import pandas as pd
import os
import argparse
import json
import time
import sys
import concurrent.futures
import threading
from tqdm import tqdm

model_id = {
    "v3": "9dc913a037774fc0b248376905c85da5 ",
    "r1": "4bd107bff85941239e27b1509eccfe98 ",
    "local": "local_model"  # 本地模型ID，可根据实际情况修改
}

# 虚拟API密钥，用于本地模型不需要认证但OpenAI客户端要求提供密钥的情况
DUMMY_API_KEY = "sk-dummy-key-for-local-model-not-requiring-auth"


def complete_cot_v3(question, answer, api_key, base_url, bk=None):
    """
    使用 DeepSeek-V3 模型补全 CoT
    :param question: 输入问题
    :param answer: 输入答案
    :param api_key: DeepSeek-V3 API Key
    :param base_url: DeepSeek-V3 API 的基础 URL
    :return: 补全后的 CoT
    """
    # 初始化 OpenAI 客户端
    client = OpenAI(api_key=api_key, base_url=base_url)
    
    # 构造提示词
    system_prompt = "你是一个推理专家，能够补全从问题得到答案的中间详细推理过程(Chain-of-Thought)。"
    user_prompt = f"""问题:
    {question}

    答案:
    {answer}

    参考知识：
    {bk}

    请直接输出从问题得到该答案的思考推理过程，不要用markdown格式，要使用人类的语言习惯输出思考推理过程，思考推理过程尽可能长并详细。
    """
    # 调用模型
    completion = client.chat.completions.create(
        model=model_id["v3"],
        messages=[
            {"role": "system", "content": system_prompt},
            {"role": "user", "content": user_prompt}
        ],
        stream=False  # 非流式输出
    )
    # 提取模型生成的内容
    cot_content = completion.choices[0].message.content.strip()
    return cot_content


def complete_cot_r1(question, api_key, base_url, bk=None):
    """
    使用 DeepSeek-R1 模型补全 CoT
    :param question: 输入问题
    :param api_key: DeepSeek-R1 API Key
    :param base_url: DeepSeek-R1 API 的基础 URL
    :param bk: 参考知识文本
    :return: 补全后的 CoT
    """
    # 初始化 OpenAI 客户端
    client = OpenAI(api_key=api_key, base_url=base_url)
    
    # 构造提示词
    bk_text = f"\n\n参考知识：\n{bk}" if bk else ""
    user_prompt = f"请回答以下问题：\n{question}{bk_text}"
    # 调用模型
    completion = client.chat.completions.create(
        model=model_id["r1"],
        messages=[
            {"role": "user", "content": user_prompt}
        ],
        stream=False  # 非流式输出
    )
    # 提取模型生成的内容
    cot_content = completion.choices[0].message.content.strip().split('<think>')[-1].split('</think>')[0].lstrip().rstrip()
    return cot_content


def complete_cot_local(question, answer, api_key, base_url, model_name, cot_format="think", bk=None, timeout=60):
    """
    使用本地部署的兼容OpenAI接口的模型补全 CoT
    :param question: 输入问题
    :param answer: 输入答案
    :param api_key: API Key (可能为空，取决于本地部署配置)
    :param base_url: 本地模型服务的基础 URL (例如 http://localhost:8000/v1)
    :param model_name: 本地模型名称
    :param cot_format: 思维链标记格式，可选 "think" 或 "custom"
    :param bk: 参考知识文本
    :param timeout: 请求超时时间（秒）
    :return: 补全后的 CoT
    """
    try:
        # 初始化 OpenAI 客户端 - OpenAI客户端总是需要一个API密钥
        # 如果用户指定不使用API密钥，我们提供一个虚拟密钥
        client = OpenAI(api_key=api_key, base_url=base_url, timeout=timeout)
        
        # 构造提示词
        if answer:
            # 如果提供了答案，则生成从问题到答案的推理过程
            system_prompt = "你是一个推理专家，能够补全从问题得到答案的中间详细推理过程(Chain-of-Thought)。"
            
            # 添加参考知识到提示词
            bk_text = f"\n参考知识：\n{bk}" if bk else ""
            
            user_prompt = f"""问题:
            {question}

            答案:
            {answer}
            {bk_text}

            请直接输出从问题得到该答案的思考推理过程，不要用markdown格式，要使用人类的语言习惯输出思考推理过程，思考推理过程尽可能长并详细。
            """
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ]
        else:
            # 如果没有提供答案，则请模型生成带有思维链的完整回答
            instruction = "请先思考问题的解决方法，然后给出回答。思考过程用<think>标签包裹。"
            
            # 添加参考知识到提示词
            bk_text = f"\n参考知识：\n{bk}" if bk else ""
            
            user_prompt = f"{instruction}\n\n请回答以下问题：\n{question}{bk_text}"
            messages = [
                {"role": "user", "content": user_prompt}
            ]
        
        # 调用模型
        completion = client.chat.completions.create(
            model=model_name,
            messages=messages,
            stream=False,  # 非流式输出
            timeout=timeout  # 设置超时时间
        )
        
        # 提取模型生成的内容
        raw_content = completion.choices[0].message.content.strip()
        
        # 处理输出格式
        if not answer:  # 如果是直接生成带思维链的回答
            if '<think>' in raw_content and '</think>' in raw_content:
                # 提取<think></think>标签之间的内容
                match = re.search(r'<think>(.*?)</think>', raw_content, re.DOTALL)
                if match:
                    cot_content = match.group(1).strip()
                else:
                    cot_content = raw_content
            else:
                # 如果没有标签，假设整个输出就是思维过程
                cot_content = raw_content
        else:  # 如果是根据问题和答案生成中间推理
            cot_content = raw_content
            
        return cot_content
    except Exception as e:
        print(f"调用本地模型出错: {str(e)}")
        print(f"问题: {question[:50]}...")
        print(f"当前API URL: {base_url}")
        print(f"当前模型名称: {model_name}")
        if "api_key client option must be set" in str(e):
            print("错误原因: OpenAI客户端需要API密钥。请使用--api_key参数提供一个密钥，或使用--no_api_key参数使用虚拟密钥。")
        elif "Illegal header value" in str(e):
            print("错误原因: API认证头部格式错误，请检查API密钥格式或尝试不提供API密钥")
        elif "Connection" in str(e):
            print("错误原因: 无法连接到模型服务器，请检查服务器地址和端口是否正确，以及服务器是否正在运行")
        elif "Timeout" in str(e) or "timeout" in str(e):
            print("错误原因: 请求超时，服务器响应时间过长。可能是服务器负载过高或网络问题")
        return None


# 添加JSONL写入函数
def write_jsonl(data, output_file):
    """
    将数据以JSONL格式写入文件
    :param data: 要写入的数据，列表格式，每个元素是一个字典
    :param output_file: 输出文件路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        for item in data:
            json_str = json.dumps(item, ensure_ascii=False)
            f.write(json_str + '\n')
    print(f"已写入{len(data)}条记录到{output_file}")


# 添加JSON写入函数
def write_json(data, output_file):
    """
    将数据以JSON格式写入文件
    :param data: 要写入的数据，列表格式，每个元素是一个字典
    :param output_file: 输出文件路径
    """
    with open(output_file, 'w', encoding='utf-8') as f:
        json_str = json.dumps(data, ensure_ascii=False, indent=2)
        f.write(json_str)
    print(f"已写入{len(data)}条记录到{output_file}")


# 添加JSON读取函数
def read_json(file_path):
    """
    读取JSON文件
    :param file_path: JSON文件路径
    :return: 解析后的数据
    """
    with open(file_path, 'r', encoding='utf-8') as f:
        data = json.load(f)
    return data


def read_background_knowledge(file_path):
    """
    从文本文件中读取背景知识
    :param file_path: 背景知识文件路径
    :return: 背景知识文本
    """
    if not file_path or not os.path.exists(file_path):
        return None
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read().strip()
        print(f"已从 {file_path} 读取参考知识，长度：{len(content)} 字符")
        return content
    except Exception as e:
        print(f"读取参考知识文件失败: {str(e)}")
        return None


# 示例调用
if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Distill CoT data from DeepSeek-V3.")
    # 训练模式参数
    parser.add_argument("--input_file", type=str, required=True, help="The source data.")
    parser.add_argument("--output", required=True, type=str, help="The output data.")
    parser.add_argument("--api_key", type=str, default="", help="API KEY for the model.")
    parser.add_argument("--model", type=str, default="v3", choices=["v3", "r1", "local"], help="Model type to use.")
    
    # 为本地模型添加的参数
    parser.add_argument("--base_url", type=str, default="http://localhost:8000/v1", 
                      help="Base URL for the model API (default: http://localhost:8000/v1)")
    parser.add_argument("--local_model_name", type=str, default="local_model",
                      help="Name of the local model when using 'local' model type")
    parser.add_argument("--cot_format", type=str, default="think", choices=["think", "custom"],
                      help="Format for CoT tags (think: <think>...</think>, custom: configurable)")
    parser.add_argument("--with_answer", action="store_true", 
                      help="If set, the local model will receive both question and answer to generate CoT")
    parser.add_argument("--output_format", type=str, default="json", choices=["auto", "jsonl", "json"],
                      help="Output format: json (default) or jsonl")
    parser.add_argument("--no_api_key", action="store_true",
                      help="If set, will use a dummy API key for local models that don't require auth but OpenAI client needs one")
    parser.add_argument("--max_retries", type=int, default=3,
                      help="Maximum number of retries for API calls")
    parser.add_argument("--retry_delay", type=int, default=2,
                      help="Delay between retries in seconds")
    parser.add_argument("--timeout", type=int, default=120,
                      help="Timeout for API calls in seconds (default: 120)")
    parser.add_argument("--save_every", type=int, default=1,
                      help="Save results every N records (default: 1)")
    # 添加参考知识文件和并发处理参数
    parser.add_argument("--background_knowledge", type=str, default="",
                      help="Path to a text file containing background knowledge to include in prompts")
    parser.add_argument("--concurrency", type=int, default=1,
                      help="Number of concurrent requests to the model API (default: 1)")
    
    args = parser.parse_args()

    # 替换为你的 API Key 和基础 URL
    if args.no_api_key:
        API_KEY = DUMMY_API_KEY  # 使用虚拟API密钥
        print(f"注意: 使用虚拟API密钥 '{DUMMY_API_KEY[:10]}...'")
    elif args.api_key:
        API_KEY = args.api_key
    else:
        API_KEY = os.environ.get("DeepSeek_API_KEY", "")
        if not API_KEY:
            if args.model != "local":
                print("警告: 未提供API密钥，可能导致认证失败")
            else:
                # 本地模型但没有提供API密钥，使用虚拟密钥
                API_KEY = DUMMY_API_KEY
                print(f"注意: 本地模型未提供API密钥，使用虚拟API密钥 '{DUMMY_API_KEY[:10]}...'")
    
    if args.model in ["v3", "r1"]:
        BASE_URL = "https://wishub-x1.ctyun.cn/v1"
    else:
        BASE_URL = args.base_url
        
    model_type = args.model
     
    # 读取文件
    file_path = args.input_file
    o_path = args.output
    file_type = file_path.split('.')[-1]
    
    # 读取参考知识文件
    background_knowledge = None
    if args.background_knowledge:
        background_knowledge = read_background_knowledge(args.background_knowledge)
    
    # 设置输出格式
    if args.output_format == "auto":
        o_type = o_path.split('.')[-1]
        if o_type not in ['xlsx', 'csv', 'json', 'jsonl']:
            print(f"警告: 输出类型 {o_type} 不受支持，将默认使用JSON格式")
            o_type = "json"
    else:
        o_type = args.output_format
    
    # 确保输出文件扩展名正确
    if o_type == "jsonl" and not o_path.endswith('.jsonl'):
        o_path = o_path if o_path.endswith('.json') else f"{o_path}.jsonl"
        print(f"输出格式已设置为JSONL，输出文件路径已调整为: {o_path}")
    elif o_type == "json" and not o_path.endswith('.json'):
        o_path = f"{o_path}.json"
        print(f"输出格式已设置为JSON，输出文件路径已调整为: {o_path}")
    
    # 检查输入文件格式
    assert file_type in ['xlsx', 'csv', 'json'], f"{file_path}: type {file_type}, not supported."
    
    # 如果输出文件已存在，询问是否覆盖
    if os.path.exists(o_path):
        print(f"警告: 输出文件 {o_path} 已存在。")
        choice = input("是否覆盖现有文件? (y/n): ").strip().lower()
        if choice != 'y':
            print("操作已取消")
            sys.exit(0)
        # 清空文件
        open(o_path, 'w').close()
        print(f"已清空文件 {o_path}")

    if file_type == 'xlsx':
        df = pd.read_excel(file_path)
    elif file_type == 'csv':
        df = pd.read_csv(file_path)
    elif file_type == 'json':
        try:
            # 先尝试作为普通JSON读取（支持数组格式）
            data = read_json(file_path)
            if isinstance(data, list):
                # 如果是数组格式，转换为DataFrame
                df = pd.DataFrame(data)
            else:
                # 否则尝试作为JSONL读取
                df = pd.read_json(file_path, lines=True)
        except:
            # 如果上面的方法失败，尝试作为JSONL读取
            df = pd.read_json(file_path, lines=True)

    # 查看数据
    print(df.head())
    print(f"共加载 {len(df)} 条数据")

    def apply_cot(row):
        # 重试机制
        for attempt in range(args.max_retries):
            try:
                if model_type == 'v3':
                    cot_result = complete_cot_v3(row['Question'], row['Response'], API_KEY, BASE_URL, background_knowledge)
                elif model_type == 'r1':
                    cot_result = complete_cot_r1(row['Question'], API_KEY, BASE_URL, background_knowledge)
                else:  # local model
                    print(f"正在调用本地模型生成思维链，请耐心等待...")
                    if args.with_answer:
                        cot_result = complete_cot_local(
                            row['Question'], 
                            row['Response'], 
                            API_KEY, 
                            BASE_URL, 
                            args.local_model_name, 
                            args.cot_format, 
                            background_knowledge, 
                            args.timeout
                        )
                    else:
                        cot_result = complete_cot_local(
                            row['Question'], 
                            None, 
                            API_KEY, 
                            BASE_URL, 
                            args.local_model_name, 
                            args.cot_format, 
                            background_knowledge, 
                            args.timeout
                        )
                
                # 如果成功获取结果，返回格式化的思维链
                if cot_result:
                    print("成功获取思维链！")
                    # 确保CoT不包含多余的<think>标签
                    if "<think>" in cot_result and "</think>" in cot_result:
                        cot_result = re.sub(r'<think>|</think>', '', cot_result).strip()
                    return cot_result
                else:
                    # 如果结果为None但没有抛出异常，说明函数内部已处理了错误
                    if attempt < args.max_retries - 1:
                        print(f"重试中... ({attempt+1}/{args.max_retries})")
                        time.sleep(args.retry_delay)
                    else:
                        print("达到最大重试次数，跳过此条目")
                        return "ERROR: 无法生成思维链"
            except KeyboardInterrupt:
                print("\n用户中断操作。正在保存已处理的数据...")
                return "ERROR: 用户中断"
            except Exception as e:
                print(f"Error generating CoT: {e}")
                if "timeout" in str(e).lower():
                    print(f"请求超时。可能是模型生成时间过长，尝试增加超时时间 (当前: {args.timeout}秒)")
                if attempt < args.max_retries - 1:
                    print(f"重试中... ({attempt+1}/{args.max_retries})")
                    time.sleep(args.retry_delay)
                else:
                    print("达到最大重试次数，跳过此条目")
                    return "ERROR: 无法生成思维链"
        
        return "ERROR: 无法生成思维链"

    # 处理并发执行的函数
    def process_batch(batch_df, lock):
        batch_results = []
        for _, row in batch_df.iterrows():
            # 检查这个问题是否已经处理过
            with lock:
                if any(item.get('Question', '') == row['Question'] for item in results):
                    print(f"问题已存在于结果中，跳过: {row['Question'][:30]}...")
                    continue
            
            print(f"处理问题: {row['Question'][:50]}...")
            # 生成思维链
            cot = apply_cot(row)
            if cot == "ERROR: 用户中断":
                return None  # 表示用户中断
            
            current_row = {
                "Question": row['Question'],
                "Complex_CoT": cot,
                "Response": row['Response'] if 'Response' in row else ""
            }
            
            batch_results.append(current_row)
            
            # 如果是JSONL格式，立即写入文件
            if o_type == 'jsonl':
                with lock:
                    with open(o_path, 'a', encoding='utf-8') as f:
                        f.write(json.dumps(current_row, ensure_ascii=False) + '\n')
            
        return batch_results

    # 处理每一行数据
    results = []
    success_count = 0
    error_count = 0
    
    # 如果是JSON文件并且已存在，尝试加载已有数据
    partial_results = []
    if o_type == 'json' and os.path.exists(o_path) and os.path.getsize(o_path) > 0:
        try:
            partial_results = read_json(o_path)
            if isinstance(partial_results, list) and len(partial_results) > 0:
                print(f"找到已有的JSON文件，包含 {len(partial_results)} 条记录")
                results = partial_results
                # 计算有多少条记录已经处理过
                processed_ids = {item.get('Question', '')[:50] for item in partial_results}
                df = df[~df['Question'].str[:50].isin(processed_ids)]
                print(f"跳过已处理的记录，剩余 {len(df)} 条待处理")
        except Exception as e:
            print(f"读取已有JSON文件失败: {e}，将创建新文件")
    
    print(f"\n开始处理数据...")
    
    # 创建线程锁，用于并发写入时的同步
    lock = threading.Lock() if args.concurrency > 1 else None
    
    try:
        # 单线程模式
        if args.concurrency <= 1:
            for index, row in tqdm(df.iterrows(), total=len(df), desc="处理进度"):
                print(f"\n处理第 {index+1}/{len(df)} 条数据:")
                print(f"问题: {row['Question'][:50]}...")
                
                # 检查这个问题是否已经处理过
                if any(item.get('Question', '') == row['Question'] for item in results):
                    print(f"问题已存在于结果中，跳过")
                    continue
                
                # 生成思维链
                cot = apply_cot(row)
                if cot == "ERROR: 用户中断":
                    print("用户中断处理，保存已处理数据...")
                    break
                    
                # 不修改原始DataFrame，而是创建新的字典
                current_row = {
                    "Question": row['Question'],
                    "Complex_CoT": cot,
                    "Response": row['Response'] if 'Response' in row else ""
                }
                
                # 统计成功/失败数
                if cot.startswith("ERROR:"):
                    error_count += 1
                else:
                    success_count += 1
                    
                print("-------")
                print(f"思维链: {cot[:100]}..." if len(cot) > 100 else f"思维链: {cot}")
                
                # 添加到结果列表
                results.append(current_row)
                
                # 根据输出格式和保存频率保存中间结果
                if o_type == 'jsonl':
                    # 增量写入JSONL文件
                    with open(o_path, 'a', encoding='utf-8') as f:
                        f.write(json.dumps(current_row, ensure_ascii=False) + '\n')
                elif o_type == 'json' and (index + 1) % args.save_every == 0:
                    # 每隔 save_every 条数据保存一次完整的JSON文件
                    write_json(results, o_path)
        else:
            # 多线程并发模式
            print(f"使用 {args.concurrency} 个并发线程处理数据...")
            
            # 将数据分成小批次
            batch_size = max(1, len(df) // args.concurrency)
            batches = [df.iloc[i:i+batch_size] for i in range(0, len(df), batch_size)]
            print(f"数据已分成 {len(batches)} 个批次")
            
            with concurrent.futures.ThreadPoolExecutor(max_workers=args.concurrency) as executor:
                # 提交所有批次的任务
                future_to_batch = {executor.submit(process_batch, batch, lock): i for i, batch in enumerate(batches)}
                
                # 处理完成的任务结果
                for future in tqdm(concurrent.futures.as_completed(future_to_batch), total=len(batches), desc="批次处理进度"):
                    batch_index = future_to_batch[future]
                    try:
                        batch_results = future.result()
                        if batch_results is None:  # 用户中断
                            print("用户中断处理，保存已处理数据...")
                            break
                        
                        # 更新结果
                        with lock:
                            for item in batch_results:
                                results.append(item)
                                if item['Complex_CoT'].startswith("ERROR:"):
                                    error_count += 1
                                else:
                                    success_count += 1
                            
                            # 定期保存JSON结果
                            if o_type == 'json' and len(results) % args.save_every == 0:
                                write_json(results, o_path)
                                
                        print(f"批次 {batch_index+1}/{len(batches)} 完成，处理了 {len(batch_results)} 条数据")
                    except Exception as e:
                        print(f"处理批次 {batch_index} 时出错: {e}")
    except KeyboardInterrupt:
        print("\n用户中断操作。正在保存已处理的数据...")
    finally:
        # 验证结果数据格式
        if results:
            if o_type == 'json':
                # 确保每个记录都有必要的字段
                for item in results:
                    if 'Question' not in item:
                        print(f"警告: 发现没有Question字段的记录，将被跳过")
                    if 'Complex_CoT' not in item:
                        print(f"警告: 发现没有Complex_CoT字段的记录，将被添加空值")
                        item['Complex_CoT'] = ""
                    if 'Response' not in item:
                        print(f"警告: 发现没有Response字段的记录，将被添加空值")
                        item['Response'] = ""
                
                # 过滤掉不完整的记录
                valid_results = [item for item in results if 'Question' in item]
                if len(valid_results) < len(results):
                    print(f"注意: 过滤了 {len(results) - len(valid_results)} 条不完整的记录")
                    results = valid_results
        
        # 确保最终结果被保存
        if o_type == 'json' and results:
            write_json(results, o_path)
            # 验证写入的文件
            try:
                test_read = read_json(o_path)
                print(f"验证: JSON文件格式正确，包含 {len(test_read)} 条记录")
            except Exception as e:
                print(f"警告: 写入的JSON文件可能存在格式问题: {e}")
        elif o_type == 'jsonl':
            print(f"JSONL文件已增量保存到: {o_path}")
        
        # 打印处理统计信息
        print("\n处理完成或中断!")
        print(f"总数据: {len(df)} 条")
        print(f"成功: {success_count} 条")
        print(f"失败: {error_count} 条")
        print(f"未处理: {len(df) - success_count - error_count} 条")
        
        # 打印输出信息
        if o_type == 'jsonl':
            print(f"已成功处理并保存{len(results)}条记录到{o_path} (JSONL格式)")
        elif o_type == 'json':
            print(f"已成功处理并保存{len(results)}条记录到{o_path} (JSON格式)")
        else:
            print(f"数据已保存为{o_type}格式: {o_path}")
        
        print(f"数据集已准备完成，可用于DeepSeek-R1训练")