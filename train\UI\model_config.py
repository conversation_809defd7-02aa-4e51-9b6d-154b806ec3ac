"""
模型配置文件
用于存储模型路径配置，使得评估、训练等功能可以共用同一套配置
"""

import os
import json

# 模型配置文件路径
CONFIG_FILE = os.path.join(os.path.dirname(os.path.abspath(__file__)), "model_paths.json")

# 默认模型路径
DEFAULT_MODEL_PATHS = {
    "Qwen2.5-1.5B-Instruct": "/ssd1/align/Qwen2.5-1.5B-Instruct",
    "Qwen2.5-7B-Instruct": "/ssd1/align/Qwen2.5-7B-Instruct",
    "DeepSeek-R1-Distill-Qwen-1.5B": "/ssd2/llm/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"
}

# 默认模型
DEFAULT_MODEL = "Qwen2.5-1.5B-Instruct"

# 读取模型配置
def load_model_config():
    """读取模型配置文件，如果不存在则使用默认配置"""
    if os.path.exists(CONFIG_FILE):
        try:
            with open(CONFIG_FILE, 'r', encoding='utf-8') as f:
                config = json.load(f)
                # 验证路径是否存在
                model_paths = config.get("model_paths", DEFAULT_MODEL_PATHS)
                for model_name, path in model_paths.items():
                    if not os.path.exists(path):
                        print(f"警告: 模型 {model_name} 的路径不存在: {path}")
                return model_paths, config.get("default_model", DEFAULT_MODEL)
        except Exception as e:
            print(f"读取模型配置文件失败: {e}")
    
    # 如果配置文件不存在或读取失败，使用默认配置并保存
    # 验证默认路径是否存在
    for model_name, path in DEFAULT_MODEL_PATHS.items():
        if not os.path.exists(path):
            print(f"警告: 默认模型 {model_name} 的路径不存在: {path}")
    
    save_model_config(DEFAULT_MODEL_PATHS, DEFAULT_MODEL)
    return DEFAULT_MODEL_PATHS, DEFAULT_MODEL

# 保存模型配置
def save_model_config(model_paths, default_model=None):
    """保存模型配置到文件"""
    try:
        config = {
            "model_paths": model_paths,
            "default_model": default_model if default_model else DEFAULT_MODEL
        }
        with open(CONFIG_FILE, 'w', encoding='utf-8') as f:
            json.dump(config, f, ensure_ascii=False, indent=2)
        return True
    except Exception as e:
        print(f"保存模型配置文件失败: {e}")
        return False

# 添加或更新模型路径
def add_or_update_model(model_name, model_path):
    """添加或更新模型路径"""
    model_paths, default_model = load_model_config()
    model_paths[model_name] = model_path
    return save_model_config(model_paths, default_model)

# 删除模型
def delete_model(model_name):
    """删除模型配置"""
    model_paths, default_model = load_model_config()
    if model_name in model_paths:
        del model_paths[model_name]
        # 如果删除的是默认模型，重新设置默认模型
        if model_name == default_model and model_paths:
            default_model = list(model_paths.keys())[0]
        return save_model_config(model_paths, default_model)
    return False

# 设置默认模型
def set_default_model(model_name):
    """设置默认模型"""
    model_paths, _ = load_model_config()
    if model_name in model_paths:
        return save_model_config(model_paths, model_name)
    return False

# 获取模型路径
def get_model_path(model_name):
    """获取指定模型的路径"""
    model_paths, default_model = load_model_config()
    # 如果指定的模型名称存在，返回对应路径
    if model_name in model_paths:
        return model_paths[model_name]
    # 如果指定的模型名称不存在，返回默认模型路径
    elif default_model in model_paths:
        return model_paths[default_model]
    # 如果默认模型也不存在，返回第一个模型路径
    elif model_paths:
        return list(model_paths.values())[0]
    # 如果没有任何模型配置，返回None
    return None

# 获取所有模型名称
def get_model_names():
    """获取所有配置的模型名称"""
    model_paths, _ = load_model_config()
    return list(model_paths.keys())

# 获取默认模型名称
def get_default_model():
    """获取默认模型名称"""
    _, default_model = load_model_config()
    return default_model

# 初始化加载配置
model_paths, default_model = load_model_config() 