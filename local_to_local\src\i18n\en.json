{"menuTabVideo": "Realtime Video Call", "menuTabAudio": "Realtime Voice Call", "menuTabChatbot": "<PERSON><PERSON><PERSON>", "videoCallBtn": "Call AI Assistant", "audioCallBtn": "Call AI Assistant", "hangUpBtn": "Hang Up", "notReadyBtn": "Not ready yet, please wait", "skipMessageBtn": "Skip this message", "feedbackDialogTitle": "Feedback issue", "modelConfigTitle": "Model Config", "audioInterruptionBtn": "Speech Interruption", "audioInterruptionTips": "When the \"voice interruption\" mode is enabled, it allows users to interrupt the model while it is speaking. The model will immediately terminate the previous round of generation and respond to the user's latest question.", "yes": "Yes", "no": "No", "videoQualityBtn": "HD Mode", "videoQualityTips": "When the \"high resulation\" mode is enabled, the model will perform high resolution encoding on the last frame, allowing the model to see more detailed parts.", "high": "High", "low": "Low", "vadThresholdBtn": "VAD Threshold", "vadThresholdTips": "The VAD threshold indicates how long the sound needs to be silent before triggering inference. If the VAD threshold is too low, it may trigger accidentally during speech pauses, while if it's too high, it will result in slower initial response.", "assistantPromptBtn": "Task Prompt", "assistantPromptTips": "Model task instructions are used to support different task objectives.", "useVoicePromptBtn": "Tone Color Prompt", "voiceClonePromptInput": "Tone Color Prompt", "voiceClonePromptTips": "Tone Color Prompt tips", "audioChoiceBtn": "Audio Choice", "defaultAudioBtn": "Default Audio", "customizationBtn": "Customization: Upload Audio", "toneColorOptions": "Voice Options", "toneColorOptionsTips": "We have provided a selection of sample tone colors, and you also have the option to choose \"none\" and instruct the model to create a new tone color.", "nullOption": "<PERSON><PERSON>", "defaultOption": "Female 1(<PERSON><PERSON><PERSON>)", "femaleOption": "Female 2", "maleOption": "Male 1"}