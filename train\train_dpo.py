import os
import torch
import argparse
from datasets import Dataset
import json
from transformers import AutoToken<PERSON>, AutoModelForCausalLM, TrainingArguments
from peft import PeftModel, get_peft_model, LoraConfig
from trl import DPOTrainer

# 数据路径相同
dataset_path = 'data/data.json'

# 加载数据
with open(dataset_path, 'r', encoding='utf-8') as file:
    data = json.load(file)
    train_data = data[:int(len(data)*0.9)]
    
# 处理为DPO格式
dpo_dataset = []
for x in train_data:
    # 有思考过程的回答作为chosen
    chosen = f"<think>\n{x['Complex_CoT']}\n</think>\n{x['Response']}"
    # 纯回答作为rejected
    rejected = x['Response']
    
    dpo_dataset.append({
        "prompt": x['Question'],
        "chosen": chosen,
        "rejected": rejected
    })

# 转换为Dataset对象
train_dataset = Dataset.from_list(dpo_dataset)

# 加载模型与tokenizer
model_path = "/ssd1/align/Qwen2.5-7B-Instruct"
sft_path = "./test_outputs/origin_lora/"

tokenizer = AutoTokenizer.from_pretrained(model_path)
model = AutoModelForCausalLM.from_pretrained(model_path, torch_dtype=torch.bfloat16)

# 合并SFT权重
if os.path.exists(sft_path):
    model = PeftModel.from_pretrained(model, sft_path)
    model = model.merge_and_unload()

# 训练
trainer = DPOTrainer(
    model=model,
    tokenizer=tokenizer,
    train_dataset=train_dataset,
    peft_config=LoraConfig(
        r=8, 
        lora_alpha=16,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj", "gate_proj", "up_proj", "down_proj"]
    ),
    max_length=1024,
    max_prompt_length=512,
    beta=0.1,
    per_device_train_batch_size=1,
    gradient_accumulation_steps=8,
    max_steps=3000,
    learning_rate=2e-5,
    output_dir="./outputs/dpo_model"
)

trainer.train()
trainer.save_model() 