# DeepSeek-R1 复现项目

本项目是对 DeepSeek-R1 论文中提出的思维链（Chain of Thought）训练方法的复现实现。项目包含了从预训练模型到 SFT（Supervised Fine-tuning）再到 RL（Reinforcement Learning）的完整训练流程。

## 项目结构

```
.
├── Dockerfile              # Docker环境配置文件
├── run_docker.sh          # Docker运行脚本
├── train_sft.py           # SFT训练脚本
├── train_rl.py            # RL训练脚本
├── eval.py                # 模型评估脚本
├── cot.py                # CoT相关工具函数
├── run_sft.sh            # SFT训练启动脚本
├── run_rl.sh             # RL训练启动脚本
└── sampled_medical_all.json  # 医疗领域数据集
```

## 环境要求

- Python 3.8+
- CUDA 11.7+
- 16GB+ GPU显存（推荐）

主要依赖包：
- unsloth
- transformers
- trl
- sentence-transformers
- datasets
- wandb

## 快速开始

### 使用 Docker（推荐）

1. 构建 Docker 镜像：
```bash
bash run_docker.sh
```

### 训练流程

#### 1. SFT 训练

SFT训练支持两种模式：
- origin：基于 Qwen2.5-1.5B-Instruct 模型训练
- distill：基于 DeepSeek-R1-Distill-Qwen-1.5B 模型训练

运行训练：
```bash
bash run_sft.sh
```

主要参数说明：
- --train_mode: 训练模式 [origin/distill]
- --system_message: 系统提示类型 [empty/instruction]
- --batch_size: 批次大小
- --lr: 学习率
- --epoch_num: 训练轮数
- --lora_rank: LoRA秩
- --lora_alpha: LoRA alpha值

#### 2. RL 训练

RL训练使用GRPO（Generalized Reward Proximal Optimization）算法，支持与SFT相同的两种模式。

运行训练：
```bash
bash run_rl.sh
```

主要参数说明：
- --train_mode: 训练模式 [origin/distill]
- --batch_size: 批次大小
- --lr: 学习率
- --max_steps: 最大训练步数
- --num_generations: 每个样本生成的候选回答数
- --lora_rank: LoRA秩
- --lora_alpha: LoRA alpha值

### 模型评估

使用eval.py进行模型评估：
```bash
python eval.py --model_path [训练后的模型路径]
```

## 数据格式

训练数据（sampled_medical_all.json）格式如下：
```json
{
    "Question": "问题文本",
    "Complex_CoT": "推理过程",
    "Response": "最终答案"
}
```

## COT数据生成

```bash
python cot.py --input_file data/data.xlsx --output pic.json --model local --local_model_name DeepSeek-R1-Distill-Qwen-32B --base_url http://27.159.93.61:7981/v1/ --background_knowledge data/参考.txt --concurrency 4
#使用场景的jsonl数据
python cot.py --input_file data/scene_next_0705.jsonl --output scene.json --model local --local_model_name DeepSeek-R1-Distill-Qwen-32B --base_url http://27.159.93.61:7981/v1/ --background_knowledge data/参考.txt --concurrency 20
#需要映射参数
python train/cot.py --input_file your_data.jsonl --output output.json --model local --question_field query --response_field answer
```

## 训练输出

- SFT训练输出位于 `./output/` 目录
- RL训练输出位于 `./outputs/` 目录
- 训练日志通过wandb记录
- SFT启动脚本
  ```
   CUDA_VISIBLE_DEVICES=0 python train_sft.py --train_mode origin --system_message instruction --batch_size 8 --lr 2e-4 --epoch_num 1 --output_path ./test_outputs --max_seq_length 1024
  ```
- RL训练启动脚本
  ```
  CUDA_VISIBLE_DEVICES=0 python train_rl.py \
  --train_mode origin \
  --merge_sft_weights \
  --sft_weights_path ./test_outputs/origin_lora/ \
  --max_seq_length 1024 \
  --max_prompt_length 512 \
  --max_completion_length 512 \
  --lora_rank 8 \
  --lora_alpha 16 \
  --lora_dropout 0.05 \
  --batch_size 12 \
  --lr 2e-5 \
  --max_steps 6000 \
  --num_generations 2 \
  --gradient_accumulation_steps 8 \
  --warmup_ratio 0.1 \
  --hide_thinking
  ```

CUDA_VISIBLE_DEVICES=0 python train_rl.py \
--train_mode origin \
--max_seq_length 1024 \
--max_prompt_length 512 \
--max_completion_length 512 \
--lora_rank 8 \
--lora_alpha 16 \
--lora_dropout 0.0 \
--batch_size 3 \
--lr 2e-5 \
--max_steps 2000 \
--num_generations 2 \
--gradient_accumulation_steps 4 \
--warmup_ratio 0.03

CUDA_VISIBLE_DEVICES=0 setsid nohup  python train_rl.py \
--train_mode origin \
--max_seq_length 1024 \
--max_prompt_length 512 \
--max_completion_length 512 \
--lora_rank 8 \
--lora_alpha 16 \
--lora_dropout 0.0 \
--batch_size 3 \
--lr 2e-5 \
--max_steps 2000 \
--num_generations 2 \
--gradient_accumulation_steps 4 \
--warmup_ratio 0.03 &


# SFT阶段（使用80%数据）
CUDA_VISIBLE_DEVICES=0 python train_sft.py --train_mode origin --epoch_num 5 --batch_size 32

# RL阶段（使用剩余20%验证数据 + 增强后的训练数据）
python train_rl.py --train_mode origin \
    --max_steps 4000 \
    --batch_size 8 \
    --data_mix_ratio 0.5  # 50%新数据+50%增强数据

CUDA_VISIBLE_DEVICES=0 setsid nohup  python train_rl.py   --train_mode origin   --batch_size 6   --lr 1e-5   --max_steps 2000   --num_generations 8   --lora_rank 8   --lora_alpha 16.0   --gradient_accumulation_steps 8 --max_seq_length 512 &

## 注意事项

1. 训练过程中会使用LoRA进行参数高效微调
2. 推荐使用足够大的GPU显存以获得更好的训练效果
3. 可以通过调整batch_size和gradient_accumulation_steps来适应不同的显存大小

## 引用

如果您使用了本项目的代码，请引用原始DeepSeek-R1论文。

## 许可证

本项目遵循MIT许可证。
 ```json
{
  "prompt": [
    {
      "role": "system",
      "content": "\nRespond in the following format:\n<think>\n推理过程\n</think>\n最终答案\n"
    },
    {
      "role": "user",
      "content": "当前车辆的状态如下：（播放的歌名：无\n音乐的歌手：无\n请求人位置：副驾\n当前车内温度：23\n当前车外温度：23\n当前车辆档位：P\n当前车辆速度：0\n当前主驾空调温度：\n当前副驾空调温度：\n当前空调风量：0）\n\n用户要求如下：朋友们都坐好了，现在请帮我调节一个既舒适又有趣的场景模式吧。\n"
    }
  ],
  "question": "当前车辆的状态如下：（播放的歌名：无\n音乐的歌手：无\n请求人位置：副驾\n当前车内温度：23\n当前车外温度：23\n当前车辆档位：P\n当前车辆速度：0\n当前主驾空调温度：\n当前副驾空调温度：\n当前空调风量：0）\n\n用户要求如下：朋友们都坐好了，现在请帮我调节一个既舒适又有趣的场景模式吧。\n",
  "thinking": "好，我需要帮助这个用户设计一个既舒适又有趣的场景模式。首先，我得分析当前车辆的状态。当前的车内温度和车外温度都是23度，车辆处于P档，速度为0，说明车辆是静止的，可能是在停车后或者等待出发。\n\n用户的要求是让朋友们都坐好之后，调节一个舒适且有趣的场景。考虑到车内温度已经舒适，但风量为0，可能空调没有打开。所以，我需要考虑开启空调，并调整风量和温度，以增加舒适度。\n\n此外，用户提到要有趣，可能需要加入一些娱乐或氛围元素。例如，播放喜欢的音乐，开启氛围灯，或者播放一些轻松的背景音乐。但根据当前状态，播放的歌名和歌手都是"无"，所以可能需要启动媒体播放，比如 choosesongs功能。\n\n另一方面，考虑舒适性，可以调整座椅加热或通风，使用香氛来提升车内空气质量，或者开启除雾功能防止起雾。同时，调整车窗或车门，确保车内通风良好。\n\n再者，用户在副驾驶位置，可能需要特别调整副驾驶的空调温度和风向。风量可以适当调高，确保舒适。\n\n最后，确保执行动作不超过五个，并且逻辑一致，不会产生冲突。例如，开启空调和调整温度应该同时进行。\n\n综合考虑，我会建议以下步骤：\n1. 开启空调，并调整主驾和副驾的温度到舒适值。\n2. 调整风量到适宜的水平。\n3. 提供娱乐功能，如播放音乐或开启氛围灯。\n4. 确保车门和车窗状态良好，可能需要开窗透气。",
  "answer": "#场景名称:\n舒适趣味模式\n#触发条件:\n无\n#状态条件:\n无\n#执行动作:\n主驾温度：23\n副驾温度：23\n前排风量：3档\n后排吹风：吹面吹脚\nQQ音乐搜索: 轻松愉快的音乐 \n播放控制器: 播放 \n香氛种类: 晨雾 \n香氛浓度: 适中 \n香氛开关: 开\n#生效时间:\n无\n"
}
 ```