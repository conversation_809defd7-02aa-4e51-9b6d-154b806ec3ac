# python

Use the gradio_client Python library or the @gradio/client Javascript package to query the app via API.PythonJavascript1. Install the client if you don't already have it installed.copy$ pip install gradio_client2. Find the API endpoint below corresponding to your desired function in the app. Copy the code snippet, replacing the placeholder values with your own input data. Or 🪄 Use the API Recorder to automatically generate your API requests.api_name: /generate_songcopyfrom gradio_client import Client, fileclient = Client("http://************:7860/")result = client.predict(		lyric="[intro-short][verse]夜晚的街灯闪烁我漫步在熟悉的角落回忆像潮水般涌来你的笑容如此清晰在心头无法抹去那些曾经的甜蜜如今只剩我独自回忆[verse]手机屏幕亮起是你发来的消息简单的几个字却让我泪流满面曾经的拥抱温暖如今却变得遥远我多想回到从前重新拥有你的陪伴[chorus]回忆的温度还在你却已不在我的心被爱填满却又被思念刺痛音乐的节奏奏响我的心却在流浪没有你的日子我该如何继续向前[outro-short]",		description=None,		prompt_audio=None,		genre="Pop",		cfg_coef=1.5,		temperature=0.9,		top_k=50,		api_name="/generate_song")print(result)Accepts 7 parameters:lyric str Default: "[intro-short] [verse] 夜晚的街灯闪烁 我漫步在熟悉的角落 回忆像潮水般涌来 你的笑容如此清晰 在心头无法抹去 那些曾经的甜蜜 如今只剩我独自回忆 [verse] 手机屏幕亮起 是你发来的消息 简单的几个字 却让我泪流满面 曾经的拥抱温暖 如今却变得遥远 我多想回到从前 重新拥有你的陪伴 [chorus] 回忆的温度还在 你却已不在 我的心被爱填满 却又被思念刺痛 音乐的节奏奏响 我的心却在流浪 没有你的日子 我该如何继续向前 [outro-short]"The input value that is provided in the "歌词" Textbox component.description str | None Default: NoneThe input value that is provided in the "歌曲描述(可选)" Textbox component.prompt_audio filepath | None Default: NoneThe input value that is provided in the "提示音频(可选)" Audio component.genre Literal['Pop', 'R&B', 'Dance', 'Jazz', 'Folk', 'Rock', 'Chinese Style', 'Chinese Tradition', 'Metal', 'Reggae', 'Chinese Opera', 'Auto'] Default: "Pop"The input value that is provided in the "风格选择(可选)" Radio component.cfg_coef float Default: 1.5The input value that is provided in the "CFG系数" Slider component.temperature float Default: 0.9The input value that is provided in the "温度" Slider component.top_k float Default: 50The input value that is provided in the "Top-K" Slider component.Returns tuple of 2 elements[0] filepathThe output value that appears in the "生成歌曲" Audio component.[1] Dict[Any, Any]The output value that appears in the "生成信息" Json component.