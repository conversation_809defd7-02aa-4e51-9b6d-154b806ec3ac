# AI音乐创作 API 接口文档

## 基本信息
- **API 地址**: http://27.159.93.61:7860/
- **接口数量**: 1个API端点
- **框架**: Gradio

## 接口详情

### 生成歌曲接口

**接口名称**: `/generate_song`

**描述**: 该接口用于根据歌词和可选的音频或文本提示生成自定义歌曲。

#### 请求参数

接受7个参数：

1. **lyric** (str, 必需)
   - 描述: 歌词内容
   - 格式要求:
     - 每段代表一个分段，以结构标签开头，以空行结尾
     - 每行代表一个句子，不建议使用标点符号
     - 分段[intro], [inst], [outro]不应包含歌词
     - [verse], [chorus], [bridge]则需要歌词
   - 默认值: 示例歌词（关于回忆的歌曲）

2. **description** (str | None, 可选)
   - 描述: 歌曲描述
   - 默认值: None

3. **prompt_audio** (filepath | None, 可选)
   - 描述: 提示音频文件
   - 默认值: None

4. **genre** (str, 可选)
   - 描述: 音乐风格选择
   - 可选值:
     - 'Pop' (流行)
     - 'R&B' (节奏布鲁斯)
     - 'Dance' (舞曲)
     - 'Jazz' (爵士)
     - 'Folk' (民谣)
     - 'Rock' (摇滚)
     - 'Chinese Style' (中国风)
     - 'Chinese Tradition' (传统)
     - 'Metal' (金属)
     - 'Reggae' (雷鬼)
     - 'Chinese Opera' (戏曲)
     - 'Auto' (自动)
   - 默认值: "Pop"

5. **cfg_coef** (float, 可选)
   - 描述: CFG系数
   - 默认值: 1.5

6. **temperature** (float, 可选)
   - 描述: 温度参数
   - 默认值: 0.9

7. **top_k** (float, 可选)
   - 描述: Top-K参数
   - 默认值: 50

#### 返回结果

返回包含2个元素的元组：

1. **[0]** (filepath)
   - 描述: 生成的歌曲音频文件路径

2. **[1]** (Dict[Any, Any])
   - 描述: 生成信息的JSON对象

## 使用示例

### Python客户端

```python
from gradio_client import Client, file

client = Client("http://27.159.93.61:7860/")

result = client.predict(
    lyric="[intro-short] [verse] 夜晚的街灯闪烁 我漫步在熟悉的角落 回忆像潮水般涌来 你的笑容如此清晰 在心头无法抹去 那些曾经的甜蜜 如今只剩我独自回忆 [verse] 手机屏幕亮起 是你发来的消息 简单的几个字 却让我泪流满面 曾经的拥抱温暖 如今却变得遥远 我多想回到从前 重新拥有你的陪伴 [chorus] 回忆的温度还在 你却已不在 我的心被爱填满 却又被思念刺痛 音乐的节奏奏响 我的心却在流浪 没有你的日子 我该如何继续向前 [outro-short]",
    description=None,
    prompt_audio=None,
    genre="Pop",
    cfg_coef=1.5,
    temperature=0.9,
    top_k=50,
    api_name="/generate_song"
)

print(result)
```

### 安装客户端

```bash
# Python客户端
$ pip install gradio_client

# JavaScript客户端
$ npm install @gradio/client
```

## 注意事项

1. 歌词格式需要严格按照要求，使用结构标签来分段
2. 音频文件需要是有效的文件路径
3. 所有可选参数都有默认值，可以根据需要调整
4. 返回的音频文件路径可以用于下载生成的歌曲
5. 生成信息包含详细的处理结果和元数据

## API录制器

该服务还提供了🪄 API Recorder功能，可以自动生成API请求代码。