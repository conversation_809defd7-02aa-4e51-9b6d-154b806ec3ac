import argparse
from unsloth import FastLanguageModel
from trl import SFTTrainer
from transformers import TrainingArguments
from unsloth import is_bfloat16_supported
from datasets import load_dataset, DatasetDict
import json
import random
import tempfile


def formatting_prompts_func_orgin(examples):
    """
    格式化origin模式下的输入文本，用于训练。
    """
    im_start = tokenizer("<|im_start|>").input_ids
    im_end = tokenizer("<|im_end|>").input_ids
    EOS_TOKEN = tokenizer.eos_token
    inputs = examples["Question"]
    cots = examples["Complex_CoT"]
    outputs = examples["Response"]
    texts = []
    for input_text, cot, output in zip(inputs, cots, outputs):
        # 随机添加前缀扰动
        perturbations = ["请仔细思考后回答：", "根据以下问题：", "需要分步骤解答："]
        if random.random() < 0.3:
            input_text = random.choice(perturbations) + input_text
        answer = f"<think>\n{cot}\n</think>\n{output}"
        formatted_input_str = (
            tokenizer.decode(im_start) + system_message + tokenizer.decode(im_end) + "\n" +
            tokenizer.decode(tokenizer("<|im_start|>user").input_ids) + "\n" +
            input_text + tokenizer.decode(im_end) + "\n" +
            tokenizer.decode(tokenizer("<|im_start|>assistant").input_ids) + "\n" +
            answer + tokenizer.decode(im_end) + "\n" + EOS_TOKEN
        )
        texts.append(formatted_input_str)
    return {
        "text": texts
    }


def formatting_prompts_func_distill(examples):
    """
    格式化distill模式下的输入文本，用于训练。
    """
    EOS_TOKEN = tokenizer.eos_token
    formatted_input_str_template = """
{Question}

<think>
{Complex_CoT} 
</think>
{Response}
    """
    inputs = examples["Question"]
    cots = examples["Complex_CoT"]
    outputs = examples["Response"]
    texts = []
    for input_text, cot, output in zip(inputs, cots, outputs):
        formatted_input_str = formatted_input_str_template.format(
            Question=input_text,
            Complex_CoT=cot,
            Response=output
        )
        formatted_input_str = tokenizer.bos_token + formatted_input_str + EOS_TOKEN
        texts.append(formatted_input_str)
    return {
        "text": texts
    }


# 模型权重路径
model_weight_paths = {
    #"origin": "/ssd1/align/Qwen2.5-7B-Instruct",
    "origin": "/ssd1/align/Qwen2.5-1.5B-Instruct",
    "distill": "/ssd2/llm/deepseek-ai/DeepSeek-R1-Distill-Qwen-1.5B"
}

# 提示词中系统部分内容
system_messages = {
    "empty": "You are a helpful assistant.",
    "instruction": "You are a helpful assistant. The assistant first thinks about the reasoning process in the mind and then provides the user with the answer. The reasoning process enclosed within <think> </think> tags, and answer after </think> tag, respectively, i.e., <think> reasoning process here </think> answer here."
}

# 数据存放路径
data_path = 'data/data.json'

# 拼接提示词的函数映射，不同的训练模型使用不同的function
formatting_prompts_funcs = {
    "origin": formatting_prompts_func_orgin,
    "distill": formatting_prompts_func_distill
}


def train(args):
    """
    根据命令行参数训练语言模型。
    """
    global system_message
    system_message = system_messages[args.system_message]

    # 加载模型和分词器
    global tokenizer
    model, tokenizer = FastLanguageModel.from_pretrained(
        model_name=model_weight_paths[args.train_mode],
        max_seq_length=args.max_seq_length,
        dtype=None,
    )

    # 加载数据集并根据训练模式进行处理
    with open(data_path, 'r', encoding='utf-8') as file:
        data = json.load(file)
        # 按8:1:1划分训练/验证/测试集
        train_data = data[:int(0.8*len(data))]
        valid_data = data[int(0.8*len(data)):int(0.9*len(data))]
        test_data = data[int(0.9*len(data)):]
        
        # 保存为临时文件
        import tempfile
        with tempfile.TemporaryDirectory() as tmp_dir:
            # 保存训练集
            train_path = f"{tmp_dir}/train.json"
            with open(train_path, 'w', encoding='utf-8') as f:
                json.dump(train_data, f, ensure_ascii=False)
            
            # 保存验证集
            valid_path = f"{tmp_dir}/valid.json"
            with open(valid_path, 'w', encoding='utf-8') as f:
                json.dump(valid_data, f, ensure_ascii=False)
            
            # 保存测试集
            test_path = f"{tmp_dir}/test.json"
            with open(test_path, 'w', encoding='utf-8') as f:
                json.dump(test_data, f, ensure_ascii=False)
            
            # 加载数据集
            dataset = load_dataset(
                'json',
                data_files={
                    'train': train_path,
                    'valid': valid_path,
                    'test': test_path,
                }
            )
    
    dataset = dataset.filter(lambda x: len(x['Question']) + len(x['Complex_CoT']) + len(x['Response']) <= 1024)
    dataset = dataset.map(formatting_prompts_funcs[args.train_mode], batched=True)
    train_dataset = dataset['train']

    # 加载lora
    model = FastLanguageModel.get_peft_model(
        model,
        r=args.lora_rank,
        target_modules=["q_proj", "k_proj", "v_proj", "o_proj",
                        "gate_proj", "up_proj", "down_proj", ],
        lora_alpha=args.lora_alpha,
        lora_dropout=args.lora_dropout,
        bias="none",
        use_gradient_checkpointing="unsloth",
        random_state=3407,
        use_rslora=False,
        loftq_config=None,
    )

    # 构造输出目录
    output_dir = f"{args.output_path}/{args.train_mode}_{args.system_message}_bs{args.batch_size}_lr{args.lr}_epoch{args.epoch_num}_lora_r{args.lora_rank}_lora_alpha{args.lora_alpha}/"
    # 初始化SFT训练器
    trainer = SFTTrainer(
        model=model,
        tokenizer=tokenizer,
        train_dataset=train_dataset,
        dataset_text_field="text",
        max_seq_length=args.max_seq_length,
        dataset_num_proc=64,
        packing=False,
        args=TrainingArguments(
            per_device_train_batch_size=args.batch_size,
            gradient_accumulation_steps=1,
            warmup_steps=400,
            # max_steps=4000,
            num_train_epochs=args.epoch_num, # For longer training runs!
            learning_rate=args.lr,
            fp16=not is_bfloat16_supported(),
            bf16=is_bfloat16_supported(),
            logging_steps=1,
            # optim="adamw_8bit",
            optim="adamw_torch_fused",
            weight_decay=0.01,
            # lr_scheduler_type="linear",
            lr_scheduler_type="cosine",
            seed=3407,
            output_dir=output_dir,
            #report_to="wandb",
            report_to="none",
            save_steps=1000,
            save_total_limit=10,
        ),
    )
    trainer_stats = trainer.train()  # 开始训练

    # 保存完整16bit模型(用于后续RL训练)
    model.save_pretrained_merged(
        output_dir, 
        tokenizer,
        save_method = "merged_16bit",  # 推荐使用16bit精度保存
        # save_method = "lora",        # 如果显存不足可保留lora权重
    )
    tokenizer.save_pretrained(output_dir)


if __name__ == "__main__":
    parser = argparse.ArgumentParser(description="Train a language model with specified parameters.")
    # 训练模式参数
    parser.add_argument("--system_message", type=str, default="empty",
                        help="The system message to use.")
    parser.add_argument("--train_mode", type=str, default="origin",
                        choices=["origin", "distill"],
                        help="The training mode to use.")
        
    # 模型配置参数
    parser.add_argument("--max_seq_length", type=int, default=2048,
                        help="The maximum sequence length for the model.")
    parser.add_argument("--lora_rank", type=int, default=8,
                        help="The rank of the LoRA adapter.")
    parser.add_argument("--lora_alpha", type=float, default=16.0,
                        help="The alpha value for the LoRA adapter.")
    parser.add_argument("--lora_dropout", type=float, default=0.05,
                        help="The dropout probability for the LoRA adapter.")
    
    # 训练配置参数
    parser.add_argument("--batch_size", type=int, default=32,
                        help="The batch size for training.")
    parser.add_argument("--lr", type=float, default=3e-4,
                        help="The learning rate for training.")
    parser.add_argument("--epoch_num", type=float, default=10,
                        help="The epochs for training.")
    
    # 输出路径
    parser.add_argument("--output_path", type=str, default='./output',
                        help="The output directory to save the trained model and tokenizer.")

    args = parser.parse_args()
    train(args)  # 调用训练函数

