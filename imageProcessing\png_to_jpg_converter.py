import os
from PIL import Image
import time

def convert_png_to_jpg(source_dir, dest_dir):
    """
    将源目录中的 PNG 图像转换为 JPG 格式并保存在目标目录中。

    参数:
        source_dir (str): 包含 PNG 文件的目录路径。
        dest_dir (str): 保存 JPG 文件的目录路径。
    """
    # 如果目标目录不存在，则创建它
    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)
        print(f"创建目录: {dest_dir}")

    # 遍历源目录中的文件
    for filename in os.listdir(source_dir):
        if filename.lower().endswith(".png"):
            source_path = os.path.join(source_dir, filename)
            
            # 生成基于时间戳的文件名
            timestamp = int(time.time() * 1000)
            new_filename = f"{timestamp}.jpg"
            dest_path = os.path.join(dest_dir, new_filename)
            
            try:
                # 打开 PNG 图像
                with Image.open(source_path) as img:
                    # 转换为 RGB 模式（JPG 不支持 alpha 透明通道）
                    rgb_img = img.convert('RGB')
                    # 保存为 JPG 格式
                    rgb_img.save(dest_path, "jpeg")
                    print(f"成功将 {filename} 转换为 {new_filename}")
            except Exception as e:
                print(f"转换 {filename} 失败: {e}")

if __name__ == "__main__":
    # 定义源目录和目标目录
    # 注意：在 Windows 上，最好使用原始字符串 (r"...") 或双反斜杠 ("\\") 来处理路径
    source_directory = r"D:\下载\png"
    destination_directory = r"D:\下载\jpg"

    print(f"开始转换... 源目录: {source_directory}, 目标目录: {destination_directory}")
    convert_png_to_jpg(source_directory, destination_directory)
    print("转换完成。") 