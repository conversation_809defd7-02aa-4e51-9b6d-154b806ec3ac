<template>
    <div class="home-page">
        <div class="home-page-header glass-effect tech-glow">
            <!-- 隐藏logo区域 -->
            <!-- <div class="home-page-header-logo">
                <SvgIcon name="miniCPM2.6" class="logo-icon" />
            </div> -->
            <div class="home-page-header-menu">
                <div
                    class="home-page-header-menu-item glass-button mechanical-button"
                    v-for="(item, index) in tabList"
                    :key="item.type"
                    :class="`home-page-header-menu-item ${activeTab === item.type ? 'active' : ''} ${item.disabled ? 'disabled-tab' : ''}`"
                    @click="handleClickTab(item.type, index)"
                >
                    <span class="tech-text">{{ getMenuTab(item.type) }}</span>
                </div>
            </div>

            <div class="home-page-header-switch">
                <div class="change-language glass-effect">
                    <div
                        :class="`change-language-item glass-button mechanical-button ${language === 'en' ? 'active' : ''}`"
                        @click="handleChangeLanguage('en')"
                    >
                        <span class="tech-text">English</span>
                    </div>
                    <div
                        :class="`change-language-item glass-button mechanical-button ${language === 'zh' ? 'active' : ''}`"
                        @click="handleChangeLanguage('zh')"
                    >
                        <span class="tech-text">中文</span>
                    </div>
                </div>
            </div>
        </div>
        
        <!-- 机械分割线 -->
        <div class="mechanical-divider"></div>
        
        <div :class="`home-page-content glass-effect ${activeTab === 'chatbot' && 'no-padding'}`">
            <VoiceCallWs v-if="isWebSocket && activeTab === 'voice'" v-model="isCalling" />
            <VoiceCall v-else-if="!isWebSocket && activeTab === 'voice'" v-model="isCalling" />
            <VideoCallWs v-else-if="isWebSocket && activeTab === 'video'" v-model="isCalling" />
            <VideoCall v-else-if="!isWebSocket && activeTab === 'video'" v-model="isCalling" />
            <!-- TODO: https is required to support chatbot in iframe -->
            <iframe
                src="http://127.0.0.1:8000/"
                frameborder="0"
                width="100%"
                height="100%"
                v-else
                class="chatbot-iframe"
            />
            <div class="config-box glass-effect" v-if="activeTab !== 'chatbot'">
                <ModelConfig v-model:isCalling="isCalling" v-model:type="activeTab" />
            </div>
        </div>
    </div>
</template>

<script setup>
    import VoiceCall from './components/VoiceCall.vue';
    import VoiceCallWs from './components/VoiceCall_0105.vue';
    import VideoCall from './components/VideoCall.vue';
    import VideoCallWs from './components/VideoCall_0105.vue';
    import { useI18n } from 'vue-i18n';
    import { useRoute, useRouter } from 'vue-router';

    const route = useRoute();
    const router = useRouter();
    const typeObj = {
        0: 'video',
        1: 'voice',
        // 2: 'chatbot'  // 注释掉聊天机器人
    };
    const defaultType = typeObj[route.query.type] || 'voice';

    const { t, locale } = useI18n();
    const activeTab = ref(defaultType);
    const language = ref(localStorage.getItem('language') || 'zh');
    const isWebSocket = false;
    
    // 注释掉聊天机器人菜单项
    const tabList = ref([
        {
            type: 'video',
            text: 'Realtime Video Call'
        },
        {
            type: 'voice',
            text: 'Realtime Voice Call'
        }
        // {
        //     type: 'chatbot',
        //     text: 'Chatbot'
        // }
    ]);
    
    const isCalling = ref(false);
    const handleChangeLanguage = val => {
        console.log('val: ', val);
        language.value = val;
        locale.value = val;
        localStorage.setItem('language', val);
    };
    const getMenuTab = val => {
        let text = '';
        switch (val) {
            case 'video':
                text = t('menuTabVideo');
                break;
            case 'voice':
                text = t('menuTabAudio');
                break;
            // case 'chatbot':
            //     text = t('menuTabChatbot');
            //     break;
            default:
                break;
        }
        return text;
    };
    const handleClickTab = (val, index) => {
        activeTab.value = val;
        const port = route.query.port;
        const type = index;
        router.push({
            path: '/',
            query: {
                port,
                type
            }
        });
    };
</script>

<style lang="less" scoped>
    .home-page {
        width: 100%;
        height: 100%;
        display: flex;
        flex-direction: column;
        gap: 16px;
        
        &-header {
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px 32px;
            position: relative;
            border-radius: 20px;
            
            /* 机械科技风装饰元素 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                height: 3px;
                background: linear-gradient(90deg, 
                    transparent, 
                    rgba(100, 127, 255, 0.4), 
                    rgba(120, 158, 254, 0.6), 
                    rgba(100, 127, 255, 0.4), 
                    transparent);
                border-radius: 2px;
            }
            
            /* 底部装饰线 */
            &::after {
                content: '';
                position: absolute;
                bottom: 0;
                left: 0;
                right: 0;
                height: 1px;
                background: linear-gradient(90deg, 
                    transparent, 
                    rgba(100, 127, 255, 0.2), 
                    transparent);
            }
            
            &-menu {
                display: flex;
                align-items: center;
                gap: 8px;
                
                &-item {
                    min-width: 200px;
                    height: 52px;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                    font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;
                    font-size: 16px;
                    font-weight: 600;
                    cursor: pointer;
                    user-select: none;
                    border-radius: 16px;
                    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
                    position: relative;
                    overflow: hidden;
                    
                    /* 机械质感边框 */
                    border: 2px solid rgba(100, 127, 255, 0.15);
                    
                    &:hover {
                        transform: translateY(-3px) scale(1.02);
                        border-color: rgba(100, 127, 255, 0.3);
                    }
                    
                    &.active {
                        font-weight: 700;
                        border-color: rgba(100, 127, 255, 0.5);
                        
                        .tech-text {
                            color: #ffffff !important;
                        }
                    }
                    
                    &.disabled-tab {
                        cursor: not-allowed;
                        opacity: 0.3;
                        background: rgba(148, 163, 184, 0.1);
                        
                        .tech-text {
                            color: rgba(148, 163, 184, 0.5) !important;
                        }
                        
                        &:hover {
                            transform: none;
                        }
                    }
                }
            }
            
            &-switch {
                position: absolute;
                right: 32px;
                
                .change-language {
                    display: flex;
                    align-items: center;
                    gap: 4px;
                    padding: 6px;
                    border-radius: 16px;
                    border: 1px solid rgba(100, 127, 255, 0.15);
                    
                    &-item {
                        width: 80px;
                        height: 40px;
                        display: flex;
                        justify-content: center;
                        align-items: center;
                        font-family: 'SF Pro Display', 'Segoe UI', system-ui, sans-serif;
                        font-size: 14px;
                        font-weight: 600;
                        cursor: pointer;
                        user-select: none;
                        border-radius: 12px;
                        transition: all 0.3s ease;
                        border: 1px solid rgba(100, 127, 255, 0.1);
                        
                        &:hover {
                            transform: scale(1.05);
                            border-color: rgba(100, 127, 255, 0.25);
                        }
                        
                        &.active {
                            font-weight: 700;
                            border-color: rgba(100, 127, 255, 0.4);
                            
                            .tech-text {
                                color: #ffffff !important;
                            }
                        }
                    }
                }
            }
        }
        
        &-content {
            flex: 1;
            height: 0;
            border-radius: 24px;
            padding: 28px;
            display: flex;
            position: relative;
            overflow: hidden;
            border: 2px solid rgba(100, 127, 255, 0.1);
            
            /* 内容区域机械科技感装饰 */
            &::before {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background: 
                    linear-gradient(45deg, transparent 30%, rgba(100, 127, 255, 0.03) 50%, transparent 70%),
                    radial-gradient(circle at 20% 80%, rgba(100, 127, 255, 0.06) 0%, transparent 50%),
                    radial-gradient(circle at 80% 20%, rgba(120, 158, 254, 0.04) 0%, transparent 50%);
                pointer-events: none;
                z-index: 0;
            }
            
            /* 机械网格纹理 */
            &::after {
                content: '';
                position: absolute;
                top: 0;
                left: 0;
                right: 0;
                bottom: 0;
                background-image: 
                    linear-gradient(rgba(100, 127, 255, 0.02) 1px, transparent 1px),
                    linear-gradient(90deg, rgba(100, 127, 255, 0.02) 1px, transparent 1px);
                background-size: 20px 20px;
                pointer-events: none;
                z-index: 0;
            }
            
            /* iframe 样式优化 */
            .chatbot-iframe {
                border-radius: 20px;
                background: rgba(255, 255, 255, 0.98);
                backdrop-filter: blur(15px);
                border: 2px solid rgba(100, 127, 255, 0.15);
                overflow: hidden;
                box-shadow: 
                    0 8px 32px rgba(0, 0, 0, 0.06),
                    inset 0 1px 0 rgba(255, 255, 255, 0.8);
            }
            
            .config-box {
                width: 360px;
                margin-left: 24px;
                padding: 24px;
                border-radius: 20px;
                overflow: auto;
                position: relative;
                z-index: 1;
                border: 2px solid rgba(100, 127, 255, 0.1);
                
                /* 配置面板机械装饰线 */
                &::before {
                    content: '';
                    position: absolute;
                    left: 0;
                    top: 24px;
                    bottom: 24px;
                    width: 3px;
                    background: linear-gradient(180deg, 
                        transparent, 
                        rgba(100, 127, 255, 0.4), 
                        rgba(120, 158, 254, 0.6), 
                        rgba(100, 127, 255, 0.4), 
                        transparent);
                    border-radius: 2px;
                }
                
                /* 顶部装饰 */
                &::after {
                    content: '';
                    position: absolute;
                    top: 0;
                    left: 24px;
                    right: 24px;
                    height: 2px;
                    background: linear-gradient(90deg, 
                        transparent, 
                        rgba(100, 127, 255, 0.3), 
                        transparent);
                    border-radius: 1px;
                }
            }
            
            /* 自定义滚动条 */
            :deep(.config-box::-webkit-scrollbar) {
                width: 8px;
            }
            
            :deep(.config-box::-webkit-scrollbar-thumb) {
                background: rgba(100, 127, 255, 0.3);
                border-radius: 8px;
                border: 1px solid rgba(255, 255, 255, 0.2);
            }
            
            :deep(.config-box::-webkit-scrollbar-track) {
                background: rgba(245, 248, 252, 0.5);
                border-radius: 8px;
            }
        }
        
        .no-padding {
            padding: 6px;
            
            .chatbot-iframe {
                border-radius: 18px;
            }
        }
    }
    
    /* 机械科技感动画效果 */
    @keyframes mechanicalPulse {
        0%, 100% {
            opacity: 0.6;
            transform: scale(1);
        }
        50% {
            opacity: 1;
            transform: scale(1.01);
        }
    }
    
    @keyframes mechanicalSlideIn {
        from {
            opacity: 0;
            transform: translateY(30px) scale(0.95);
        }
        to {
            opacity: 1;
            transform: translateY(0) scale(1);
        }
    }
    
    @keyframes mechanicalGlow {
        0%, 100% {
            box-shadow: 0 0 5px rgba(100, 127, 255, 0.2);
        }
        50% {
            box-shadow: 0 0 20px rgba(100, 127, 255, 0.4);
        }
    }
    
    .home-page {
        animation: mechanicalSlideIn 0.8s cubic-bezier(0.4, 0, 0.2, 1);
    }
    
    .home-page-header {
        animation: mechanicalSlideIn 1s cubic-bezier(0.4, 0, 0.2, 1) 0.2s both;
    }
    
    .home-page-content {
        animation: mechanicalSlideIn 1.2s cubic-bezier(0.4, 0, 0.2, 1) 0.4s both;
    }
    
    /* 悬停时的机械发光效果 */
    .home-page-header-menu-item:hover {
        animation: mechanicalGlow 2s ease-in-out infinite;
    }
</style>
<style lang="less">
    /* 全局弹窗样式 - 浅色机械科技风 */
    .el-popover.el-popper.config-popover {
        padding: 24px;
        border-radius: 20px;
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(25px);
        border: 2px solid rgba(100, 127, 255, 0.15);
        box-shadow: 
            0 16px 48px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.8),
            0 0 0 1px rgba(100, 127, 255, 0.08);
        color: #1e293b;
    }
    
    /* Element Plus 组件样式覆盖 - 机械风格 */
    .el-button {
        border-radius: 16px;
        backdrop-filter: blur(15px);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        background: linear-gradient(145deg, #f8fafc, #f1f5f9);
        border: 2px solid rgba(100, 127, 255, 0.15);
        color: #334155;
        font-weight: 600;
        position: relative;
        overflow: hidden;
        
        &::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(100, 127, 255, 0.1), transparent);
            transition: left 0.5s ease;
        }
        
        &:hover {
            transform: translateY(-2px) scale(1.02);
            border-color: rgba(100, 127, 255, 0.3);
            box-shadow: 
                0 10px 25px rgba(100, 127, 255, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            
            &::before {
                left: 100%;
            }
        }
        
        &.el-button--primary {
            background: linear-gradient(135deg, rgba(100, 127, 255, 0.9), rgba(120, 158, 254, 0.8));
            border-color: rgba(100, 127, 255, 0.6);
            color: #ffffff;
        }
    }
    
    .el-input__wrapper {
        border-radius: 16px;
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        border: 2px solid rgba(100, 127, 255, 0.1);
        transition: all 0.3s ease;
        
        &:hover, &.is-focus {
            border-color: rgba(100, 127, 255, 0.3);
            box-shadow: 
                0 0 16px rgba(100, 127, 255, 0.15),
                inset 0 1px 0 rgba(255, 255, 255, 0.8);
            background: rgba(255, 255, 255, 0.95);
        }
        
        .el-input__inner {
            color: #334155;
            font-weight: 500;
        }
    }
    
    .el-select .el-input .el-input__wrapper {
        background: rgba(255, 255, 255, 0.8);
        backdrop-filter: blur(15px);
        border: 2px solid rgba(100, 127, 255, 0.1);
    }
    
    .el-select-dropdown {
        background: rgba(255, 255, 255, 0.95);
        backdrop-filter: blur(20px);
        border: 2px solid rgba(100, 127, 255, 0.15);
        border-radius: 16px;
        box-shadow: 
            0 12px 32px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
    }
    
    .el-select-dropdown__item {
        color: #334155;
        font-weight: 500;
        
        &:hover {
            background: rgba(100, 127, 255, 0.1);
            color: #1e293b;
        }
        
        &.is-selected {
            background: rgba(100, 127, 255, 0.15);
            color: #647fff;
            font-weight: 600;
        }
    }
    
    .el-slider__runway {
        background: rgba(203, 213, 225, 0.8);
        border-radius: 8px;
        border: 1px solid rgba(100, 127, 255, 0.1);
    }
    
    .el-slider__bar {
        background: linear-gradient(90deg, #647fff, #789efe);
        border-radius: 8px;
        box-shadow: 0 2px 8px rgba(100, 127, 255, 0.3);
    }
    
    .el-slider__button {
        background: linear-gradient(145deg, #ffffff, #f8fafc);
        border: 3px solid #647fff;
        box-shadow: 
            0 4px 12px rgba(100, 127, 255, 0.25),
            inset 0 1px 0 rgba(255, 255, 255, 0.8);
        transition: all 0.3s ease;
        
        &:hover {
            transform: scale(1.1);
            box-shadow: 
                0 6px 16px rgba(100, 127, 255, 0.35),
                inset 0 1px 0 rgba(255, 255, 255, 0.9);
        }
    }
    
    /* 开关组件样式 */
    .el-switch {
        &.is-checked .el-switch__core {
            background-color: #647fff;
            border-color: #647fff;
        }
        
        .el-switch__core {
            background: rgba(203, 213, 225, 0.8);
            border: 2px solid rgba(100, 127, 255, 0.2);
            
            &::after {
                background: linear-gradient(145deg, #ffffff, #f8fafc);
                box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
            }
        }
    }
    
    /* 标签样式 */
    .el-tag {
        background: rgba(100, 127, 255, 0.1);
        border: 1px solid rgba(100, 127, 255, 0.2);
        color: #334155;
        border-radius: 12px;
        font-weight: 500;
    }
</style>
