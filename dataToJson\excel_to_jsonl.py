import pandas as pd
import json
import os

def excel_to_jsonl(excel_file, output_list):
    """读取Excel文件并转换为JSONL格式的数据"""
    try:
        # 读取Excel文件
        df = pd.read_excel(excel_file)
        
        # 确保Q和A列存在
        if 'Q' not in df.columns or 'A' not in df.columns:
            print(f"错误：{excel_file} 文件中未找到Q或A列")
            return
        
        # 遍历每一行数据
        for _, row in df.iterrows():
            # 跳过空行
            if pd.isna(row['Q']) or pd.isna(row['A']):
                continue
                
            # 创建JSON对象
            json_obj = {
                "instruction": str(row['Q']).strip(),
                "input": "",
                "output": str(row['A']).strip()
            }
            
            # 添加到输出列表
            output_list.append(json_obj)
            
    except Exception as e:
        print(f"处理文件 {excel_file} 时发生错误：{str(e)}")

def main():
    # 指定源数据目录和输出目录
    source_dir = "sourceData"
    output_dir = "jsonl"
    
    # 创建输出目录（如果不存在）
    os.makedirs(output_dir, exist_ok=True)
    
    # 存储所有数据的列表
    all_data = []
    
    # 处理目录中的所有Excel文件
    excel_files = [
        "副本大福卡扩展问.xlsx",
        "副本畅享卡套餐.xlsx"
    ]
    
    for excel_file in excel_files:
        file_path = os.path.join(source_dir, excel_file)
        if os.path.exists(file_path):
            print(f"正在处理文件：{excel_file}")
            excel_to_jsonl(file_path, all_data)
        else:
            print(f"文件不存在：{file_path}")
    
    # 将所有数据写入JSONL文件
    output_file = os.path.join(output_dir, "combined_data.jsonl")
    try:
        with open(output_file, 'w', encoding='utf-8') as f:
            for item in all_data:
                f.write(json.dumps(item, ensure_ascii=False) + '\n')
        print(f"成功生成JSONL文件：{output_file}")
        print(f"总共处理了 {len(all_data)} 条数据")
    except Exception as e:
        print(f"写入JSONL文件时发生错误：{str(e)}")

if __name__ == "__main__":
    main() 