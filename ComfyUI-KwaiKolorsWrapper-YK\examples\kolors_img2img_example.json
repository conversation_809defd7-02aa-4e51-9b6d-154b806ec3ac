{"last_node_id": 17, "last_link_id": 23, "nodes": [{"id": 6, "type": "DownloadAndLoadKolorsModel", "pos": [201, 368], "size": {"0": 315, "1": 82}, "flags": {}, "order": 0, "mode": 0, "outputs": [{"name": "kolors_model", "type": "KOLORSMODEL", "links": [16], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "DownloadAndLoadKolorsModel"}, "widgets_values": ["Kwai-<PERSON><PERSON><PERSON>/Kolors", "fp16"]}, {"id": 15, "type": "Note", "pos": [200, 636], "size": {"0": 273.5273742675781, "1": 149.5546417236328}, "flags": {}, "order": 1, "mode": 0, "properties": {"text": ""}, "widgets_values": ["Text encoding takes the most VRAM, quantization can reduce that a lot.\n\nApproximate values I have observed:\nfp16 - 12 GB\nquant8 - 8-9 GB\nquant4 - 4-5 GB\n\nquant4 reduces the quality quite a bit, 8 seems fine"], "color": "#432", "bgcolor": "#653"}, {"id": 13, "type": "DownloadAndLoadChatGLM3", "pos": [206, 522], "size": {"0": 274.5334167480469, "1": 58}, "flags": {}, "order": 2, "mode": 0, "outputs": [{"name": "chatglm3_model", "type": "CHATGLM3MODEL", "links": [14], "shape": 3}], "properties": {"Node name for S&R": "DownloadAndLoadChatGLM3"}, "widgets_values": ["quant8"]}, {"id": 11, "type": "VAELoader", "pos": [201, 247], "size": {"0": 315, "1": 58}, "flags": {}, "order": 3, "mode": 0, "outputs": [{"name": "VAE", "type": "VAE", "links": [12, 20], "shape": 3}], "properties": {"Node name for S&R": "VAELoader"}, "widgets_values": ["sdxl.vae.safetensors"]}, {"id": 17, "type": "LoadImage", "pos": [714, -115], "size": {"0": 261.8788146972656, "1": 356.6060791015625}, "flags": {}, "order": 4, "mode": 0, "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [21], "shape": 3}, {"name": "MASK", "type": "MASK", "links": null, "shape": 3}], "properties": {"Node name for S&R": "LoadImage"}, "widgets_values": ["ComfyUI_temp_pkpxa_00007_.png", "image"]}, {"id": 16, "type": "VAEEncode", "pos": [1006, 270], "size": {"0": 210, "1": 46}, "flags": {}, "order": 6, "mode": 0, "inputs": [{"name": "pixels", "type": "IMAGE", "link": 21, "slot_index": 0}, {"name": "vae", "type": "VAE", "link": 20, "slot_index": 1}], "outputs": [{"name": "LATENT", "type": "LATENT", "links": [23], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEEncode"}}, {"id": 14, "type": "<PERSON><PERSON>sSampler", "pos": [1011, 371], "size": {"0": 315, "1": 266}, "flags": {}, "order": 7, "mode": 0, "inputs": [{"name": "kolors_model", "type": "KOLORSMODEL", "link": 16}, {"name": "kolors_embeds", "type": "KOLORS_EMBEDS", "link": 17}, {"name": "latent", "type": "LATENT", "link": 23}], "outputs": [{"name": "latent", "type": "LATENT", "links": [18], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "<PERSON><PERSON>sSampler"}, "widgets_values": [1024, 1024, 1000102404233414, "fixed", 25, 5, "EulerDiscreteScheduler", 0.75]}, {"id": 12, "type": "KolorsTextEncode", "pos": [519, 529], "size": {"0": 457.28936767578125, "1": 225.28656005859375}, "flags": {}, "order": 5, "mode": 0, "inputs": [{"name": "chatglm3_model", "type": "CHATGLM3MODEL", "link": 14, "slot_index": 0}], "outputs": [{"name": "kolors_embeds", "type": "KOLORS_EMBEDS", "links": [17], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "KolorsTextEncode"}, "widgets_values": ["illustration a wolf|\ncinematic photograph a cyborg wolf", "", 1]}, {"id": 3, "type": "PreviewImage", "pos": [1348, 469], "size": [838.7974624633789, 445.6296081542969], "flags": {}, "order": 9, "mode": 0, "inputs": [{"name": "images", "type": "IMAGE", "link": 13}], "properties": {"Node name for S&R": "PreviewImage"}}, {"id": 10, "type": "VAEDecode", "pos": [1367, 369], "size": {"0": 210, "1": 46}, "flags": {}, "order": 8, "mode": 0, "inputs": [{"name": "samples", "type": "LATENT", "link": 18}, {"name": "vae", "type": "VAE", "link": 12, "slot_index": 1}], "outputs": [{"name": "IMAGE", "type": "IMAGE", "links": [13], "shape": 3, "slot_index": 0}], "properties": {"Node name for S&R": "VAEDecode"}}], "links": [[12, 11, 0, 10, 1, "VAE"], [13, 10, 0, 3, 0, "IMAGE"], [14, 13, 0, 12, 0, "CHATGLM3MODEL"], [16, 6, 0, 14, 0, "KOLORSMODEL"], [17, 12, 0, 14, 1, "KOLORS_EMBEDS"], [18, 14, 0, 10, 0, "LATENT"], [20, 11, 0, 16, 1, "VAE"], [21, 17, 0, 16, 0, "IMAGE"], [23, 16, 0, 14, 2, "LATENT"]], "groups": [], "config": {}, "extra": {"ds": {"scale": 0.9090909090909091, "offset": {"0": 53.90248107910156, "1": 197.60369873046875}}}, "version": 0.4}