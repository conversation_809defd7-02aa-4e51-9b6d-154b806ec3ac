<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>智能图像压缩转换器</title>
    <!-- 引入JSZip库用于创建ZIP文件 -->
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }

        .container {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(15px);
            border-radius: 25px;
            box-shadow: 0 25px 50px rgba(0, 0, 0, 0.15);
            padding: 40px;
            max-width: 1000px;
            width: 100%;
            text-align: center;
        }

        .header {
            margin-bottom: 40px;
        }

        .title {
            color: #333;
            font-size: 3rem;
            font-weight: 700;
            margin-bottom: 10px;
            background: linear-gradient(135deg, #667eea, #764ba2, #f093fb);
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .subtitle {
            color: #666;
            font-size: 1.2rem;
            margin-bottom: 20px;
        }

        .features {
            display: flex;
            justify-content: center;
            gap: 30px;
            margin-bottom: 30px;
            flex-wrap: wrap;
        }

        .feature {
            display: flex;
            align-items: center;
            gap: 8px;
            color: #555;
            font-size: 0.95rem;
        }

        .feature-icon {
            font-size: 1.2rem;
        }

        .upload-area {
            border: 3px dashed #667eea;
            border-radius: 20px;
            padding: 60px 40px;
            margin-bottom: 30px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
            overflow: hidden;
        }

        .upload-area:hover {
            border-color: #f093fb;
            background: linear-gradient(145deg, #f0f4ff, #e0e8ff);
            transform: translateY(-3px);
        }

        .upload-area.dragover {
            border-color: #f093fb;
            background: linear-gradient(145deg, #e8edff, #d8e3ff);
            transform: scale(1.02);
        }

        .upload-icon {
            font-size: 4.5rem;
            color: #667eea;
            margin-bottom: 20px;
            display: block;
        }

        .upload-text {
            font-size: 1.4rem;
            color: #333;
            margin-bottom: 10px;
            font-weight: 600;
        }

        .upload-hint {
            color: #666;
            font-size: 1rem;
            line-height: 1.5;
        }

        #fileInput {
            display: none;
        }

        .settings-panel {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
            margin: 30px 0;
            text-align: left;
        }

        .setting-group {
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            padding: 20px;
            border-radius: 15px;
        }

        .setting-label {
            font-weight: 600;
            color: #333;
            margin-bottom: 15px;
            display: block;
            font-size: 1.1rem;
        }

        .format-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
        }

        .format-option {
            position: relative;
        }

        .format-radio {
            display: none;
        }

        .format-label {
            display: block;
            padding: 12px 15px;
            background: white;
            border: 2px solid #ddd;
            border-radius: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
            font-weight: 500;
        }

        .format-radio:checked + .format-label {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            border-color: #667eea;
            transform: scale(1.05);
        }

        .quality-section {
            margin-top: 15px;
        }

        .quality-display {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }

        .quality-slider {
            width: 100%;
            height: 8px;
            border-radius: 4px;
            background: #ddd;
            outline: none;
            -webkit-appearance: none;
        }

        .quality-slider::-webkit-slider-thumb {
            -webkit-appearance: none;
            appearance: none;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .quality-slider::-moz-range-thumb {
            width: 24px;
            height: 24px;
            border-radius: 50%;
            background: linear-gradient(135deg, #667eea, #764ba2);
            cursor: pointer;
            border: none;
            box-shadow: 0 2px 8px rgba(102, 126, 234, 0.3);
        }

        .size-inputs {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 10px;
            margin-top: 10px;
        }

        .size-input {
            padding: 10px 12px;
            border: 2px solid #ddd;
            border-radius: 8px;
            font-size: 1rem;
            outline: none;
            transition: border-color 0.3s ease;
        }

        .size-input:focus {
            border-color: #667eea;
        }

        .file-list {
            margin-top: 30px;
            text-align: left;
        }

        .file-item {
            background: white;
            border-radius: 15px;
            padding: 20px;
            margin-bottom: 15px;
            box-shadow: 0 5px 15px rgba(0, 0, 0, 0.08);
            display: flex;
            align-items: center;
            gap: 20px;
            border-left: 5px solid #667eea;
            transition: transform 0.2s ease;
        }

        .file-item:hover {
            transform: translateX(5px);
        }

        .file-preview {
            width: 70px;
            height: 70px;
            border-radius: 10px;
            object-fit: cover;
            border: 3px solid #eee;
        }

        .file-info {
            flex: 1;
        }

        .file-name-container {
            display: flex;
            align-items: center;
            gap: 5px;
            margin-bottom: 8px;
        }

        .file-name-input {
            background: transparent;
            border: none;
            font-weight: 600;
            color: #333;
            font-size: 1.1rem;
            outline: none;
            padding: 4px 8px;
            border-radius: 6px;
            transition: background-color 0.2s ease;
            flex: 1;
        }

        .file-name-input:focus {
            background-color: rgba(102, 126, 234, 0.1);
        }

        .file-extension {
            color: #667eea;
            font-weight: 600;
        }

        .file-details {
            color: #666;
            font-size: 0.9rem;
            margin-bottom: 10px;
        }

        .progress-container {
            margin: 10px 0;
        }

        .progress-bar {
            width: 100%;
            height: 8px;
            background: #f0f0f0;
            border-radius: 4px;
            overflow: hidden;
        }

        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #667eea, #764ba2, #f093fb);
            border-radius: 4px;
            width: 0%;
            transition: width 0.3s ease;
        }

        .compression-stats {
            font-size: 0.85rem;
            color: #555;
            margin-top: 5px;
        }

        .file-actions {
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .status-badge {
            padding: 6px 12px;
            border-radius: 20px;
            font-size: 0.85rem;
            font-weight: 500;
        }

        .status-pending {
            background: #fff3cd;
            color: #856404;
        }

        .status-converting {
            background: #d1ecf1;
            color: #0c5460;
        }

        .status-completed {
            background: #d4edda;
            color: #155724;
        }

        .status-error {
            background: #f8d7da;
            color: #721c24;
        }

        .action-btn {
            padding: 8px 16px;
            border: none;
            border-radius: 8px;
            cursor: pointer;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all 0.2s ease;
        }

        .download-btn {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .download-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(40, 167, 69, 0.3);
        }

        .delete-btn {
            background: linear-gradient(135deg, #dc3545, #e74c3c);
            color: white;
            padding: 8px 10px;
        }

        .delete-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(220, 53, 69, 0.3);
        }

        .main-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 30px;
            flex-wrap: wrap;
        }

        .download-actions {
            display: flex;
            gap: 15px;
            justify-content: center;
            margin-top: 20px;
            flex-wrap: wrap;
        }

        .btn {
            padding: 14px 35px;
            border: none;
            border-radius: 30px;
            font-size: 1.1rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-transform: uppercase;
            letter-spacing: 0.5px;
            position: relative;
            overflow: hidden;
        }

        .btn::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s;
        }

        .btn:hover::before {
            left: 100%;
        }

        .btn-primary {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
        }

        .btn-primary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(102, 126, 234, 0.4);
        }

        .btn-danger {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
        }

        .btn-danger:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(231, 76, 60, 0.4);
        }

        .btn-secondary {
            background: linear-gradient(135deg, #95a5a6, #7f8c8d);
            color: white;
        }

        .btn-secondary:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(149, 165, 166, 0.4);
        }

        .btn-success {
            background: linear-gradient(135deg, #28a745, #20c997);
            color: white;
        }

        .btn-success:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(40, 167, 69, 0.4);
        }

        .btn-info {
            background: linear-gradient(135deg, #17a2b8, #007bff);
            color: white;
        }

        .btn-info:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(23, 162, 184, 0.4);
        }

        .stats-panel {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            margin-top: 30px;
            padding: 25px;
            background: linear-gradient(145deg, #f8f9ff, #e8edff);
            border-radius: 20px;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2.5rem;
            font-weight: 700;
            color: #667eea;
            display: block;
            margin-bottom: 5px;
        }

        .stat-label {
            color: #666;
            font-size: 0.95rem;
            font-weight: 500;
        }

        .hidden {
            display: none;
        }

        @keyframes pulse {
            0% { transform: scale(1); }
            50% { transform: scale(1.05); }
            100% { transform: scale(1); }
        }

        .converting {
            animation: pulse 2s infinite;
        }

        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 25px 20px;
            }
            
            .title {
                font-size: 2.2rem;
            }
            
            .settings-panel {
                grid-template-columns: 1fr;
                gap: 15px;
            }
            
            .format-grid {
                grid-template-columns: 1fr;
            }
            
            .upload-area {
                padding: 40px 20px;
            }
            
            .main-actions,
            .download-actions {
                flex-direction: column;
            }
            
            .stats-panel {
                grid-template-columns: 1fr 1fr;
                gap: 15px;
            }

            .file-item {
                flex-direction: column;
                text-align: center;
                gap: 15px;
            }

            .file-info {
                width: 100%;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1 class="title">智能图像压缩转换器</h1>
            <p class="subtitle">支持多种格式，智能压缩，一键批量处理</p>
            
            <div class="features">
                <div class="feature">
                    <span class="feature-icon">🔄</span>
                    <span>多格式转换</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">🗜️</span>
                    <span>智能压缩</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">📏</span>
                    <span>尺寸调整</span>
                </div>
                <div class="feature">
                    <span class="feature-icon">📦</span>
                    <span>批量下载</span>
                </div>
            </div>
        </div>
        
        <div class="upload-area" id="uploadArea">
            <span class="upload-icon">🖼️</span>
            <div class="upload-text">拖拽图片到此处或点击选择</div>
            <div class="upload-hint">
                支持 PNG、JPG、JPEG、BMP、TIFF、WebP 等格式<br>
                可同时选择多个文件进行批量处理
            </div>
        </div>
        
        <input type="file" id="fileInput" multiple accept="image/*">
        
        <div class="settings-panel hidden" id="settingsPanel">
            <div class="setting-group">
                <label class="setting-label">输出格式</label>
                <div class="format-grid">
                    <div class="format-option">
                        <input type="radio" id="format-webp" name="outputFormat" value="webp" class="format-radio" checked>
                        <label for="format-webp" class="format-label">
                            WebP<br><small>现代高效</small>
                        </label>
                    </div>
                    <div class="format-option">
                        <input type="radio" id="format-jpg" name="outputFormat" value="jpg" class="format-radio">
                        <label for="format-jpg" class="format-label">
                            JPG<br><small>通用兼容</small>
                        </label>
                    </div>
                    <div class="format-option">
                        <input type="radio" id="format-png" name="outputFormat" value="png" class="format-radio">
                        <label for="format-png" class="format-label">
                            PNG<br><small>无损透明</small>
                        </label>
                    </div>
                    <div class="format-option">
                        <input type="radio" id="format-avif" name="outputFormat" value="avif" class="format-radio">
                        <label for="format-avif" class="format-label">
                            AVIF<br><small>未来标准</small>
                        </label>
                    </div>
                </div>
                
                <div class="quality-section">
                    <div class="quality-display">
                        <span>压缩质量</span>
                        <span id="qualityValue">80%</span>
                    </div>
                    <input type="range" class="quality-slider" id="qualitySlider" min="10" max="100" value="80">
                    <div style="display: flex; justify-content: space-between; font-size: 0.8rem; color: #666; margin-top: 5px;">
                        <span>低质量 (小文件)</span>
                        <span>高质量 (大文件)</span>
                    </div>
                </div>
            </div>
            
            <div class="setting-group">
                <label class="setting-label">尺寸调整 (可选)</label>
                <div class="size-inputs">
                    <input type="number" class="size-input" id="maxWidth" placeholder="最大宽度 (px)">
                    <input type="number" class="size-input" id="maxHeight" placeholder="最大高度 (px)">
                </div>
                <div style="font-size: 0.85rem; color: #666; margin-top: 10px;">
                    留空表示不限制尺寸，将按比例缩放
                </div>
            </div>
        </div>
        
        <div class="file-list" id="fileList"></div>
        
        <div class="main-actions">
            <button class="btn btn-primary" id="convertBtn" onclick="convertAllFiles()">
                ⚡ 开始压缩转换
            </button>
            <button class="btn btn-danger hidden" id="cancelBtn" onclick="cancelConversion()">
                ❌ 取消转换
            </button>
            <button class="btn btn-secondary" id="clearBtn" onclick="clearFiles()">
                🗑️ 清空列表
            </button>
        </div>

        <div class="download-actions hidden" id="downloadActions">
            <button class="btn btn-success" id="downloadAllBtn" onclick="downloadAllFiles()">
                📥 批量下载
            </button>
            <button class="btn btn-info" id="downloadZipBtn" onclick="downloadAsZip()">
                📦 打包下载
            </button>
        </div>
        
        <div class="stats-panel hidden" id="statsPanel">
            <div class="stat-item">
                <span class="stat-number" id="totalFiles">0</span>
                <div class="stat-label">总文件数</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="processedFiles">0</span>
                <div class="stat-label">已处理</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="totalSize">0</span>
                <div class="stat-label">原始大小</div>
            </div>
            <div class="stat-item">
                <span class="stat-number" id="savedSpace">0</span>
                <div class="stat-label">节省空间</div>
            </div>
        </div>
    </div>

    <script>
        let selectedFiles = [];
        let processedFiles = [];
        let isProcessing = false;
        let processingCancelled = false;

        // 格式扩展名映射
        const formatExtensions = {
            'webp': '.webp',
            'jpg': '.jpg',
            'png': '.png',
            'avif': '.avif'
        };

        // 初始化事件监听器
        document.addEventListener('DOMContentLoaded', function() {
            const uploadArea = document.getElementById('uploadArea');
            const fileInput = document.getElementById('fileInput');
            const qualitySlider = document.getElementById('qualitySlider');

            // 上传区域点击
            uploadArea.addEventListener('click', () => {
                fileInput.click();
            });

            // 文件选择
            fileInput.addEventListener('change', handleFileSelect);

            // 拖拽事件
            uploadArea.addEventListener('dragover', handleDragOver);
            uploadArea.addEventListener('dragleave', handleDragLeave);
            uploadArea.addEventListener('drop', handleDrop);

            // 质量滑块
            qualitySlider.addEventListener('input', function() {
                document.getElementById('qualityValue').textContent = this.value + '%';
            });

            // 格式选择
            document.querySelectorAll('input[name="outputFormat"]').forEach(radio => {
                radio.addEventListener('change', updateFormatSettings);
            });
        });

        function handleFileSelect(event) {
            const files = Array.from(event.target.files);
            addFiles(files);
        }

        function handleDragOver(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.add('dragover');
        }

        function handleDragLeave(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
        }

        function handleDrop(event) {
            event.preventDefault();
            event.stopPropagation();
            document.getElementById('uploadArea').classList.remove('dragover');
            
            const files = Array.from(event.dataTransfer.files);
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length !== files.length) {
                alert('请只选择图像文件！');
            }
            
            if (imageFiles.length > 0) {
                addFiles(imageFiles);
            }
        }

        function addFiles(files) {
            const imageFiles = files.filter(file => file.type.startsWith('image/'));
            
            if (imageFiles.length === 0) {
                alert('请选择图像文件！');
                return;
            }

            imageFiles.forEach(file => {
                if (!selectedFiles.find(f => f.name === file.name && f.size === file.size)) {
                    selectedFiles.push(file);
                }
            });

            displayFiles();
            updateStats();
            showSettings();
        }

        function showSettings() {
            document.getElementById('settingsPanel').classList.remove('hidden');
        }

        function updateFormatSettings() {
            const selectedFormat = document.querySelector('input[name="outputFormat"]:checked').value;
            const qualitySlider = document.getElementById('qualitySlider');
            
            // 根据格式调整质量范围
            if (selectedFormat === 'png') {
                qualitySlider.min = 0;
                qualitySlider.max = 9;
                qualitySlider.value = 6;
                document.getElementById('qualityValue').textContent = '压缩级别 ' + qualitySlider.value;
            } else {
                qualitySlider.min = 10;
                qualitySlider.max = 100;
                qualitySlider.value = selectedFormat === 'avif' ? 75 : 80;
                document.getElementById('qualityValue').textContent = qualitySlider.value + '%';
            }
        }

        function removeFile(index) {
            if (isProcessing) {
                alert('处理进行中，无法删除文件！');
                return;
            }
            
            selectedFiles.splice(index, 1);
            
            if (processedFiles[index]) {
                URL.revokeObjectURL(processedFiles[index].url);
                processedFiles.splice(index, 1);
            }
            
            displayFiles();
            updateStats();
            
            if (selectedFiles.length === 0) {
                document.getElementById('settingsPanel').classList.add('hidden');
            }
        }

        function displayFiles() {
            const fileList = document.getElementById('fileList');
            fileList.innerHTML = '';

            selectedFiles.forEach((file, index) => {
                const fileItem = document.createElement('div');
                fileItem.className = 'file-item';
                fileItem.id = `file-${index}`;

                // 创建预览图
                const reader = new FileReader();
                reader.onload = function(e) {
                    const preview = fileItem.querySelector('.file-preview');
                    preview.src = e.target.result;
                };
                reader.readAsDataURL(file);

                // 获取选择的输出格式
                const selectedFormat = document.querySelector('input[name="outputFormat"]:checked').value;
                const nameWithoutExt = file.name.replace(/\.[^/.]+$/, "");

                fileItem.innerHTML = `
                    <img class="file-preview" alt="预览">
                    <div class="file-info">
                        <div class="file-name-container">
                            <input type="text" class="file-name-input" value="${nameWithoutExt}" 
                                   id="filename-${index}" onchange="updateFileName(${index}, this.value)">
                            <span class="file-extension">${formatExtensions[selectedFormat]}</span>
                        </div>
                        <div class="file-details">
                            原始大小: ${formatFileSize(file.size)} | 类型: ${file.type}
                        </div>
                        <div class="progress-container">
                            <div class="progress-bar">
                                <div class="progress-fill" id="progress-${index}"></div>
                            </div>
                        </div>
                        <div class="compression-stats" id="stats-${index}"></div>
                    </div>
                    <div class="file-actions">
                        <span class="status-badge status-pending" id="status-${index}">等待处理</span>
                        <button class="action-btn download-btn hidden" id="download-${index}">下载</button>
                        <button class="action-btn delete-btn" onclick="removeFile(${index})" title="删除文件">🗑️</button>
                    </div>
                `;

                fileList.appendChild(fileItem);
            });
        }

        function updateFileName(index, newName) {
            if (selectedFiles[index]) {
                selectedFiles[index].customName = newName;
            }
        }

        function updateStats() {
            const statsPanel = document.getElementById('statsPanel');
            const downloadActions = document.getElementById('downloadActions');
            const totalFiles = selectedFiles.length;
            const processedCount = processedFiles.length;
            
            // 计算总大小和节省空间
            const totalSize = selectedFiles.reduce((sum, file) => sum + file.size, 0);
            const totalProcessedSize = processedFiles.reduce((sum, file) => sum + (file.compressedSize || 0), 0);
            const savedSpace = totalSize - totalProcessedSize;

            document.getElementById('totalFiles').textContent = totalFiles;
            document.getElementById('processedFiles').textContent = processedCount;
            document.getElementById('totalSize').textContent = formatFileSize(totalSize);
            document.getElementById('savedSpace').textContent = formatFileSize(Math.max(0, savedSpace));

            if (totalFiles > 0) {
                statsPanel.classList.remove('hidden');
            } else {
                statsPanel.classList.add('hidden');
            }

            if (processedCount > 0) {
                downloadActions.classList.remove('hidden');
            } else {
                downloadActions.classList.add('hidden');
            }
        }

        function formatFileSize(bytes) {
            if (bytes < 1024) return bytes + ' B';
            if (bytes < 1024 * 1024) return (bytes / 1024).toFixed(1) + ' KB';
            return (bytes / (1024 * 1024)).toFixed(1) + ' MB';
        }

        async function convertAllFiles() {
            if (selectedFiles.length === 0) {
                alert('请先选择要处理的文件！');
                return;
            }

            isProcessing = true;
            processingCancelled = false;
            
            document.getElementById('convertBtn').classList.add('hidden');
            document.getElementById('cancelBtn').classList.remove('hidden');

            for (let i = 0; i < selectedFiles.length; i++) {
                if (processingCancelled) break;
                await processFile(selectedFiles[i], i);
            }

            isProcessing = false;
            document.getElementById('convertBtn').classList.remove('hidden');
            document.getElementById('cancelBtn').classList.add('hidden');
            
            if (!processingCancelled) {
                alert('所有文件处理完成！');
            }
        }

        function cancelConversion() {
            processingCancelled = true;
            isProcessing = false;
            
            document.getElementById('convertBtn').classList.remove('hidden');
            document.getElementById('cancelBtn').classList.add('hidden');
            
            alert('处理已取消！');
        }

        async function processFile(file, index) {
            if (processingCancelled) return;

            const statusEl = document.getElementById(`status-${index}`);
            const progressEl = document.getElementById(`progress-${index}`);
            const statsEl = document.getElementById(`stats-${index}`);
            const fileItem = document.getElementById(`file-${index}`);

            try {
                // 更新状态
                statusEl.textContent = '处理中...';
                statusEl.className = 'status-badge status-converting';
                fileItem.classList.add('converting');

                // 模拟进度
                let progress = 0;
                const progressInterval = setInterval(() => {
                    if (processingCancelled) {
                        clearInterval(progressInterval);
                        statusEl.textContent = '已取消';
                        statusEl.className = 'status-badge status-error';
                        fileItem.classList.remove('converting');
                        return;
                    }
                    progress += Math.random() * 25;
                    if (progress > 90) progress = 90;
                    progressEl.style.width = progress + '%';
                }, 100);

                // 获取设置
                const selectedFormat = document.querySelector('input[name="outputFormat"]:checked').value;
                const quality = parseInt(document.getElementById('qualitySlider').value);
                const maxWidth = parseInt(document.getElementById('maxWidth').value) || null;
                const maxHeight = parseInt(document.getElementById('maxHeight').value) || null;

                // 处理图像
                const result = await convertImage(file, selectedFormat, quality, maxWidth, maxHeight);

                clearInterval(progressInterval);
                progressEl.style.width = '100%';

                if (result.success) {
                    // 更新状态
                    statusEl.textContent = '处理完成';
                    statusEl.className = 'status-badge status-completed';
                    fileItem.classList.remove('converting');

                    // 显示压缩统计
                    const compressionRatio = ((1 - result.compressedSize / file.size) * 100).toFixed(1);
                    statsEl.innerHTML = `
                        压缩后: ${formatFileSize(result.compressedSize)} | 
                        压缩比: ${compressionRatio}% | 
                        尺寸: ${result.finalDimensions.width}×${result.finalDimensions.height}
                    `;

                    // 创建下载
                    const customName = file.customName || file.name.replace(/\.[^/.]+$/, "");
                    const fileName = `${customName}${formatExtensions[selectedFormat]}`;
                    
                    const downloadBtn = document.getElementById(`download-${index}`);
                    downloadBtn.classList.remove('hidden');
                    downloadBtn.onclick = function() {
                        const a = document.createElement('a');
                        a.href = result.url;
                        a.download = fileName;
                        document.body.appendChild(a);
                        a.click();
                        document.body.removeChild(a);
                    };

                    processedFiles.push({
                        original: file,
                        converted: result.blob,
                        url: result.url,
                        fileName: fileName,
                        compressedSize: result.compressedSize
                    });

                } else {
                    statusEl.textContent = '处理失败';
                    statusEl.className = 'status-badge status-error';
                    fileItem.classList.remove('converting');
                    statsEl.textContent = `错误: ${result.error}`;
                }

            } catch (error) {
                statusEl.textContent = '处理失败';
                statusEl.className = 'status-badge status-error';
                fileItem.classList.remove('converting');
                statsEl.textContent = `错误: ${error.message}`;
            }

            updateStats();
        }

        async function convertImage(file, format, quality, maxWidth, maxHeight) {
            return new Promise((resolve) => {
                const canvas = document.createElement('canvas');
                const ctx = canvas.getContext('2d');
                const img = new Image();

                img.onload = function() {
                    try {
                        let { width, height } = img;

                        // 尺寸调整
                        if (maxWidth || maxHeight) {
                            const ratio = Math.min(
                                maxWidth ? maxWidth / width : 1,
                                maxHeight ? maxHeight / height : 1
                            );
                            if (ratio < 1) {
                                width = Math.round(width * ratio);
                                height = Math.round(height * ratio);
                            }
                        }

                        canvas.width = width;
                        canvas.height = height;

                        // 处理透明背景
                        if (format === 'jpg') {
                            ctx.fillStyle = 'white';
                            ctx.fillRect(0, 0, width, height);
                        }

                        ctx.drawImage(img, 0, 0, width, height);

                        // 转换格式
                        const mimeType = {
                            'webp': 'image/webp',
                            'jpg': 'image/jpeg',
                            'png': 'image/png',
                            'avif': 'image/avif'
                        }[format];

                        const qualityParam = format === 'png' ? undefined : quality / 100;

                        canvas.toBlob(function(blob) {
                            if (blob) {
                                resolve({
                                    success: true,
                                    blob: blob,
                                    url: URL.createObjectURL(blob),
                                    compressedSize: blob.size,
                                    finalDimensions: { width, height }
                                });
                            } else {
                                resolve({
                                    success: false,
                                    error: '转换失败，可能不支持该格式'
                                });
                            }
                        }, mimeType, qualityParam);

                    } catch (error) {
                        resolve({
                            success: false,
                            error: error.message
                        });
                    }
                };

                img.onerror = function() {
                    resolve({
                        success: false,
                        error: '无法加载图像'
                    });
                };

                const reader = new FileReader();
                reader.onload = function(e) {
                    img.src = e.target.result;
                };
                reader.readAsDataURL(file);
            });
        }

        function clearFiles() {
            if (isProcessing) {
                const confirmed = confirm('处理正在进行中，确定要清空所有文件吗？这将取消当前处理。');
                if (confirmed) {
                    cancelConversion();
                } else {
                    return;
                }
            }

            // 清理URL
            processedFiles.forEach(file => {
                URL.revokeObjectURL(file.url);
            });

            selectedFiles = [];
            processedFiles = [];
            document.getElementById('fileList').innerHTML = '';
            document.getElementById('statsPanel').classList.add('hidden');
            document.getElementById('downloadActions').classList.add('hidden');
            document.getElementById('settingsPanel').classList.add('hidden');
            document.getElementById('fileInput').value = '';
        }

        function downloadAllFiles() {
            if (processedFiles.length === 0) {
                alert('没有可下载的文件！');
                return;
            }

            processedFiles.forEach((file, index) => {
                setTimeout(() => {
                    const a = document.createElement('a');
                    a.href = file.url;
                    a.download = file.fileName;
                    document.body.appendChild(a);
                    a.click();
                    document.body.removeChild(a);
                }, index * 200);
            });
        }

        async function downloadAsZip() {
            if (processedFiles.length === 0) {
                alert('没有可下载的文件！');
                return;
            }

            const zipBtn = document.getElementById('downloadZipBtn');
            const originalText = zipBtn.textContent;
            zipBtn.textContent = '📦 打包中...';
            zipBtn.disabled = true;

            try {
                const zip = new JSZip();
                
                for (let i = 0; i < processedFiles.length; i++) {
                    const file = processedFiles[i];
                    zip.file(file.fileName, file.converted);
                }

                const zipBlob = await zip.generateAsync({
                    type: 'blob',
                    compression: 'DEFLATE',
                    compressionOptions: { level: 6 }
                });

                const timestamp = new Date().toISOString().slice(0, 19).replace(/:/g, '-');
                const zipFileName = `compressed_images_${timestamp}.zip`;
                
                const a = document.createElement('a');
                a.href = URL.createObjectURL(zipBlob);
                a.download = zipFileName;
                document.body.appendChild(a);
                a.click();
                document.body.removeChild(a);
                
                URL.revokeObjectURL(a.href);
                alert(`已成功打包 ${processedFiles.length} 个文件！`);

            } catch (error) {
                console.error('打包失败:', error);
                alert('打包过程中出现错误，请重试！');
            } finally {
                zipBtn.textContent = originalText;
                zipBtn.disabled = false;
            }
        }
    </script>
</body>
</html> 