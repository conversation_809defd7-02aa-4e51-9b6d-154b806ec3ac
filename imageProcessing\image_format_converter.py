import os
from PIL import Image
import time

def convert_images_to_jpg(source_dir, dest_dir):
    """
    将源目录中的各种格式图像转换为 JPG 格式并保存在目标目录中。

    参数:
        source_dir (str): 包含图像文件的目录路径。
        dest_dir (str): 保存 JPG 文件的目录路径。
    """
    # 如果目标目录不存在，则创建它
    if not os.path.exists(dest_dir):
        os.makedirs(dest_dir)
        print(f"创建目录: {dest_dir}")

    # 遍历源目录中的文件
    for filename in os.listdir(source_dir):
        source_path = os.path.join(source_dir, filename)
        
        # 确保我们只处理文件
        if not os.path.isfile(source_path):
            continue

        try:
            # 尝试打开文件作为图像
            with Image.open(source_path) as img:
                # 为了减少快速处理中可能出现的文件名冲突，加入微小的延迟
                time.sleep(0.001)
                # 生成基于时间戳的文件名
                timestamp = int(time.time() * 1000)
                new_filename = f"{timestamp}.jpg"
                dest_path = os.path.join(dest_dir, new_filename)
                
                # 转换为 RGB 模式（因为 JPG 不支持透明度）
                rgb_img = img.convert('RGB')
                # 以 "jpeg" 格式保存图像, quality 参数可以调整图像质量
                rgb_img.save(dest_path, "jpeg", quality=95)
                print(f"成功将 {filename} 转换为 {new_filename}")
        except (IOError, SyntaxError, Image.UnidentifiedImageError):
            # 捕获那些不是有效图像格式的文件
            print(f"跳过非图像或无法识别的文件: {filename}")
        except Exception as e:
            print(f"转换 {filename} 时发生未知错误: {e}")

if __name__ == "__main__":
    # --- 配置 ---
    # 定义源目录和目标目录
    # 使用绝对路径以确保脚本在任何位置都能正确运行
    source_directory = r"D:\code\pythonWork\tools\image-data\data\特斯拉"
    destination_directory = r"D:\code\pythonWork\tools\image-data\data\tesla"
    # --- 配置结束 ---

    print(f"开始转换...")
    print(f"源目录: {source_directory}")
    print(f"目标目录: {destination_directory}")
    
    convert_images_to_jpg(source_directory, destination_directory)
    
    print("转换完成。") 