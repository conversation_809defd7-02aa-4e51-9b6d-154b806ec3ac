import json
import random
from typing import List, Dict, Union
import os
import gradio as gr
import torch
from transformers import AutoTokenizer, AutoModelForCausalLM

class DataAugmentation:
    def __init__(self, model_name="THUDM/chatglm3-6b"):
        self.device = "cuda" if torch.cuda.is_available() else "cpu"
        self.tokenizer = AutoTokenizer.from_pretrained(model_name, trust_remote_code=True)
        self.model = AutoModelForCausalLM.from_pretrained(model_name, trust_remote_code=True).to(self.device)
        
    def generate_variations(self, text: str, num_variations: int = 5) -> List[str]:
        """生成文本变体"""
        prompt = f"请为以下文本生成{num_variations}个语义相同但表达不同的版本，保持原意不变：\n{text}"
        
        inputs = self.tokenizer(prompt, return_tensors="pt").to(self.device)
        outputs = self.model.generate(
            **inputs,
            max_length=512,
            num_return_sequences=1,
            temperature=0.7,
            do_sample=True
        )
        
        response = self.tokenizer.decode(outputs[0], skip_special_tokens=True)
        variations = [v.strip() for v in response.split('\n') if v.strip() and v.strip() != text]
        return variations[:num_variations]

    def augment_qa_data(self, input_file: str, output_file: str, augment_q: bool = True, augment_a: bool = True, variations_per_item: int = 3):
        """增强QA数据"""
        try:
            data = []
            with open(input_file, 'r', encoding='utf-8') as f:
                for line in f:
                    data.append(json.loads(line))
            
            augmented_data = []
            for item in data:
                # 原始数据保留
                augmented_data.append(item)
                
                # 生成Q的变体
                if augment_q:
                    q_variations = self.generate_variations(item['instruction'], variations_per_item)
                    for q_var in q_variations:
                        new_item = {
                            'instruction': q_var,
                            'input': item['input'],
                            'output': item['output']
                        }
                        augmented_data.append(new_item)
                
                # 生成A的变体
                if augment_a:
                    a_variations = self.generate_variations(item['output'], variations_per_item)
                    for a_var in a_variations:
                        new_item = {
                            'instruction': item['instruction'],
                            'input': item['input'],
                            'output': a_var
                        }
                        augmented_data.append(new_item)
            
            # 保存增强后的数据
            with open(output_file, 'w', encoding='utf-8') as f:
                for item in augmented_data:
                    f.write(json.dumps(item, ensure_ascii=False) + '\n')
                    
            return f"成功生成增强数据，共{len(augmented_data)}条记录"
            
        except Exception as e:
            return f"数据增强过程中发生错误：{str(e)}"

    def augment_scene_data(self, input_file: str, output_file: str, augment_question: bool = True, augment_cot: bool = True, variations_per_item: int = 3):
        """增强场景数据"""
        try:
            with open(input_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            augmented_data = []
            for item in data:
                # 原始数据保留
                augmented_data.append(item)
                
                # 生成Question的变体
                if augment_question:
                    q_variations = self.generate_variations(item['Question'], variations_per_item)
                    for q_var in q_variations:
                        new_item = item.copy()
                        new_item['Question'] = q_var
                        augmented_data.append(new_item)
                
                # 生成Complex_CoT的变体
                if augment_cot:
                    cot_variations = self.generate_variations(item['Complex_CoT'], variations_per_item)
                    for cot_var in cot_variations:
                        new_item = item.copy()
                        new_item['Complex_CoT'] = cot_var
                        augmented_data.append(new_item)
            
            # 保存增强后的数据
            with open(output_file, 'w', encoding='utf-8') as f:
                json.dump(augmented_data, f, ensure_ascii=False, indent=2)
                
            return f"成功生成增强数据，共{len(augmented_data)}条记录"
            
        except Exception as e:
            return f"数据增强过程中发生错误：{str(e)}"

def create_augmentation_ui():
    """创建数据增强UI界面"""
    augmenter = DataAugmentation()
    
    with gr.Blocks() as app:
        gr.Markdown("# 数据集泛化工具")
        
        with gr.Tab("QA数据泛化"):
            with gr.Row():
                qa_input_file = gr.Textbox(label="输入文件路径(JSONL格式)")
                qa_output_file = gr.Textbox(label="输出文件路径(JSONL格式)")
            
            with gr.Row():
                augment_q = gr.Checkbox(label="泛化问题(Q)", value=True)
                augment_a = gr.Checkbox(label="泛化答案(A)", value=False)
                qa_variations = gr.Slider(minimum=1, maximum=10, value=3, step=1, label="每条数据的变体数量")
            
            qa_submit = gr.Button("开始泛化QA数据")
            qa_output = gr.Textbox(label="处理结果")
            
            qa_submit.click(
                fn=augmenter.augment_qa_data,
                inputs=[qa_input_file, qa_output_file, augment_q, augment_a, qa_variations],
                outputs=qa_output
            )
        
        with gr.Tab("场景数据泛化"):
            with gr.Row():
                scene_input_file = gr.Textbox(label="输入文件路径(JSON格式)")
                scene_output_file = gr.Textbox(label="输出文件路径(JSON格式)")
            
            with gr.Row():
                augment_question = gr.Checkbox(label="泛化问题(Question)", value=True)
                augment_cot = gr.Checkbox(label="泛化推理过程(Complex_CoT)", value=False)
                scene_variations = gr.Slider(minimum=1, maximum=10, value=3, step=1, label="每条数据的变体数量")
            
            scene_submit = gr.Button("开始泛化场景数据")
            scene_output = gr.Textbox(label="处理结果")
            
            scene_submit.click(
                fn=augmenter.augment_scene_data,
                inputs=[scene_input_file, scene_output_file, augment_question, augment_cot, scene_variations],
                outputs=scene_output
            )
    
    return app

if __name__ == "__main__":
    app = create_augmentation_ui()
    app.launch() 