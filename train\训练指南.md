# 基于Unsloth的医疗QA模型训练指南

## 一、监督微调（SFT）配置要点

### 1. 数据准备
```python
# 数据格式要求
{
    "Question": "发热伴咳嗽3天如何处理？",
    "Complex_CoT": "首先鉴别细菌/病毒感染...",
    "Response": "建议查血常规+C反应蛋白..."
}
```

### 2. 模型初始化
```python
from unsloth import FastLanguageModel

model, tokenizer = FastLanguageModel.from_pretrained(
    model_name = "Qwen2.5-1.5B-Instruct",
    max_seq_length = 2048,
    load_in_4bit = True,
)
```

### 3. LoRA配置
```python
model = FastLanguageModel.get_peft_model(
    model,
    r = 8,
    target_modules = ["q_proj", "k_proj", "v_proj", "o_proj",
                      "gate_proj", "up_proj", "down_proj"],
    lora_alpha = 16,
    use_gradient_checkpointing = "unsloth",
)
```

## 二、强化学习（RL）优化步骤

### 1. 奖励函数设计
```python
# 格式正确性奖励
def strict_format_reward_func(completions):
    pattern = r"^<think>\n.*?\n</think>\n\s*[^<>]+"
    return [1.0 if re.match(pattern, r) else 0.0 for r in responses]

# 语义相似度奖励
def semantic_reward(completions, reference):
    embeddings = model.encode([c["content"] for c in completions])
    return cosine_similarity(embeddings, reference_embedding)
```

### 2. GRPO训练配置
```python
from trl import GRPOConfig

training_args = GRPOConfig(
    learning_rate = 1e-5,
    per_device_train_batch_size = 4,
    max_steps = 2000,
    gradient_accumulation_steps = 4,
    optim = "paged_adamw_8bit",
)
```

## 三、Unsloth优化技巧

### 1. 内存管理
```python
# 启用显存优化
FastLanguageModel.from_pretrained(
    ...
    gpu_memory_utilization = 0.85,
    attn_implementation = "flash_attention_2",
)
```

### 2. 混合精度训练
```python
TrainingArguments(
    ...
    fp16 = not is_bfloat16_supported(),
    bf16 = is_bfloat16_supported(),
)
```

## 四、训练流程建议

1. **SFT基础训练**
   ```bash
   python train_sft.py --batch_size 16 --lr 2e-4 --epoch_num 10
   ```

2. **RL微调**
   ```bash
   python train_rl.py --batch_size 4 --lr 1e-5 --max_steps 2000
   ```

完整实现参考：[Unsloth官方示例](https://github.com/unslothai/unsloth/wiki/Medical-QA-Finetuning) 