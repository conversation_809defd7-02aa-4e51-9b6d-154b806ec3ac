# 训练集生成工具使用说明

## 功能概述

这个工具可以批量处理图片，调用视觉识别模型API，生成符合LLaMA-Factory格式的训练集数据。

## 主要特性

- ✅ 批量处理图片，支持多种格式（JPG、PNG、WEBP等）
- ✅ 智能批次管理，避免显存溢出
- ✅ **支持启用/禁用思考模式** (`<think>`标签)
- ✅ 自动生成中文问答对
- ✅ 可自定义主题和问题模板
- ✅ 进度跟踪和错误处理
- ✅ 支持OpenAI兼容API

## 文件结构

```
train-image-data/
├── training_dataset_generator.py    # 主程序
├── training_config.json            # 配置文件
├── requirements_training.txt        # 依赖文件
├── TRAINING_DATASET_README.md      # 使用说明
├── data/tesla/                     # 图片文件夹
└── json/                           # 输出文件夹
    ├── 特斯拉_training_data_20240101_120000.json
    └── 特斯拉_training_data_temp.json
```

## 安装依赖

在 `train-image-data` 目录下执行:
```bash
pip install -r requirements_training.txt
```

## 配置说明

### 1. 配置文件 (training_config.json)

```json
{
  "enable_think": true,         // 是否启用思考模式
  "batch_size": 5,              // 批次大小
  "total_count": "auto",        // 总处理数量 ("auto"或数字)
  "base_url": "...",            // API地址
  "model_name": "...",          // 模型名称
  "api_key": "1",               // API密钥
  "subject_name": "特斯拉",      // 主题名称
  "image_folder": "data/tesla", // 图片文件夹路径 (相对于脚本)
  "output_folder": "json",      // 输出文件夹路径 (相对于脚本)
  "questions": [ ... ]          // 问题模板列表
}
```

### 2. 参数说明

- **enable_think**: 
  - `true`: 启用思考模式，提示词会要求模型输出 `<think>` 标签。
  - `false`: 禁用思考模式，提示词会要求模型直接输出答案。
- **image_folder**: 图片文件夹路径，**相对于 `training_dataset_generator.py` 脚本的位置**。
- **output_folder**: 输出文件夹路径，**相对于 `training_dataset_generator.py` 脚本的位置**。

## 使用方法

**请确保在 `train-image-data` 目录下执行所有命令。**

### 1. 基本使用

```bash
cd train-image-data
python training_dataset_generator.py
```

### 2. 使用自定义配置文件

```bash
cd train-image-data
python training_dataset_generator.py my_config.json
```

## 注意事项

- **显存管理**: 批次大小不要设置过大，建议3-10之间。图片会自动压缩到1024x1024。
- **API限制**: 每个请求间有0.5秒延迟，避免超出API速率限制。
- **路径问题**: 所有路径配置（如`image_folder`）都是相对于脚本所在的位置。
- **模型兼容性**:
  - `enable_think: true` 时，请使用支持思考模式的模型。
  - `enable_think: false` 时，可以使用普通视觉模型。 